/**
 * AR Shopping Behavior Analysis
 * Advanced analytics for augmented reality shopping patterns and consumer behavior
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Smartphone,
  Scan,
  Eye,
  Hand,
  MapPin,
  ShoppingBag,
  TrendingUp,
  TrendingDown,
  Users,
  Clock,
  Target,
  Zap,
  Activity,
  Gauge,
  BarChart3,
  LineChart,
  PieChart,
  Brain,
  Camera,
  Move3d,
  Navigation,
  Crosshair,
  Focus,
  Layers,
  Box,
  Home,
  Store,
  Building2,
  Car,
  Plane,
  TreePine,
  Coffee,
  Utensils,
  Shirt,
  Watch,
  Headphones,
  Laptop,
  Gamepad2,
  Settings,
  RefreshCw,
  Search,
  Filter,
  Download,
  Upload,
  Share,
  ExternalLink,
  Plus,
  Minus,
  Edit,
  Trash,
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  ArrowLeft,
  Star,
  Heart,
  ThumbsUp,
  MessageCircle,
  Flag,
  Award,
  Trophy,
  Crown,
  Gem,
  DollarSign,
  CreditCard,
  ShoppingCart,
  Wallet,
  Globe,
  Monitor,
  Tablet,
  Glasses,
  Network,
  Wifi,
  Signal,
  Battery,
  Power,
  Link2,
  Unlock,
  Key,
  Shield,
  Lock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info,
  HelpCircle,
  BookOpen,
  FileText,
  File,
  Folder,
  Archive,
  Save,
  Mail,
  Phone,
  Bell,
  Calendar,
  Lightbulb,
  Rocket,
  Sparkles,
  Wand2
} from 'lucide-react';
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart as RechartsBarChart, Bar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ScatterChart, Scatter, Sankey, PieChart as RechartsPieChart, Pie } from 'recharts';

// TypeScript interfaces for AR shopping behavior
interface ARShoppingSession {
  id: string;
  userId: string;
  userName: string;
  deviceInfo: {
    model: string;
    os: string;
    arCapabilities: string[];
    cameraResolution: string;
    processingPower: number;
  };
  sessionContext: {
    location: 'home' | 'store' | 'office' | 'outdoor' | 'transport' | 'social';
    timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
    duration: number;
    lightingConditions: 'excellent' | 'good' | 'fair' | 'poor';
    environmentType: 'indoor' | 'outdoor' | 'mixed';
  };
  shoppingJourney: {
    entryPoint: 'app_home' | 'product_search' | 'qr_scan' | 'social_link' | 'advertisement' | 'recommendation';
    browsedCategories: string[];
    searchQueries: string[];
    productsViewed: number;
    productsScanned: number;
    comparisonsPerformed: number;
    timeToFirstInteraction: number;
  };
  arInteractions: {
    objectRecognition: number;
    surfaceDetection: number;
    handGestures: number;
    voiceCommands: number;
    touchInteractions: number;
    headMovements: number;
    eyeTracking: number;
    socialSharing: number;
  };
  spatialBehavior: {
    roomScan: boolean;
    placementAttempts: number;
    placementSuccess: number;
    movementRadius: number;
    viewingAngles: number[];
    distancePreference: number;
    stationaryTime: number;
    walkingTime: number;
  };
  productEngagement: {
    tryOnAttempts: number;
    colorVariations: number;
    sizeAdjustments: number;
    customizations: number;
    detailViews: number;
    featureExploration: number;
    socialValidation: number;
    priceChecks: number;
  };
  purchaseDecision: {
    purchaseIntent: number;
    addedToCart: boolean;
    savedForLater: boolean;
    sharedWithFriends: boolean;
    requestedInfo: boolean;
    abandonmentReason?: string;
    conversionTime?: number;
  };
  contextualFactors: {
    socialPresence: boolean;
    backgroundNoise: number;
    privacyLevel: number;
    urgencyLevel: number;
    moodIndicator: string;
    weatherCondition?: string;
  };
  technicalMetrics: {
    trackingStability: number;
    renderingQuality: number;
    latency: number;
    batteryImpact: number;
    dataUsage: number;
    crashOccurrences: number;
  };
  userFeedback: {
    usabilityRating: number;
    accuracyRating: number;
    enjoymentRating: number;
    trustRating: number;
    recommendationScore: number;
  };
  createdAt: string;
}

interface ARBehaviorMetric {
  timestamp: string;
  activeSessions: number;
  scanEvents: number;
  placementSuccess: number;
  averageEngagement: number;
  conversionRate: number;
  socialSharing: number;
  returnUsers: number;
  technicalQuality: number;
  userSatisfaction: number;
}

interface ARLocationInsight {
  location: string;
  sessionsCount: number;
  averageDuration: number;
  conversionRate: number;
  popularProducts: string[];
  uniqueBehaviors: string[];
  technicalChallenges: string[];
  successFactors: string[];
  recommendations: string[];
}

interface ARProductCategory {
  category: string;
  subcategories: string[];
  totalScans: number;
  uniqueUsers: number;
  conversionRate: number;
  averageEngagementTime: number;
  popularFeatures: string[];
  behaviorPatterns: {
    quickDeciders: number;
    detailExplorers: number;
    socialValidators: number;
    priceComparers: number;
  };
  technicalRequirements: {
    surfaceDetection: boolean;
    objectRecognition: boolean;
    handTracking: boolean;
    roomScale: boolean;
    lightingDependency: string;
  };
}

interface UserARProfile {
  userId: string;
  userName: string;
  demographics: {
    ageGroup: string;
    techSavviness: number;
    shoppingFrequency: string;
    devicePreference: string;
  };
  arExperience: {
    totalSessions: number;
    totalTimeSpent: number;
    favoriteFeatures: string[];
    expertiseLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    preferredEnvironments: string[];
  };
  shoppingBehavior: {
    averageSessionDuration: number;
    conversionRate: number;
    categoryPreferences: Record<string, number>;
    decisionMakingSpeed: 'fast' | 'moderate' | 'slow' | 'very_slow';
    socialInfluence: number;
  };
  technicalProfile: {
    deviceCapabilities: string[];
    networkPreference: string;
    qualityPreference: 'performance' | 'quality' | 'battery';
    troubleshootingAbility: number;
  };
  engagementPatterns: {
    peakUsageHours: number[];
    sessionFrequency: string;
    featureAdoption: Record<string, boolean>;
    shareAndRecommend: boolean;
  };
}

interface ARShoppingInsight {
  id: string;
  category: 'user_behavior' | 'technology_adoption' | 'conversion_optimization' | 'location_impact' | 'social_influence';
  insight: string;
  dataPoints: number;
  confidence: number;
  impact: 'low' | 'medium' | 'high' | 'critical';
  recommendation: string;
  implementation: {
    complexity: 'simple' | 'moderate' | 'complex';
    timeframe: string;
    resources: string[];
    expectedOutcome: string;
  };
  affectedSegments: string[];
  businessValue: string;
  createdAt: string;
}

// Mock data generation functions
const generateARShoppingSessions = (): ARShoppingSession[] => {
  const devices = ['iPhone 15 Pro', 'Galaxy S24 Ultra', 'Pixel 8 Pro', 'OnePlus 12', 'iPhone 14 Pro', 'Galaxy S23'];
  const osTypes = ['iOS 17', 'Android 14', 'iOS 16', 'Android 13'];
  const locations = ['home', 'store', 'office', 'outdoor', 'transport', 'social'] as const;
  const timeOfDay = ['morning', 'afternoon', 'evening', 'night'] as const;
  const categories = ['Fashion', 'Electronics', 'Home Decor', 'Beauty', 'Sports', 'Automotive', 'Books', 'Food'];
  const entryPoints = ['app_home', 'product_search', 'qr_scan', 'social_link', 'advertisement', 'recommendation'] as const;

  return Array.from({ length: 60 }, (_, i) => ({
    id: `ar-session-${i + 1}`,
    userId: `user-${Math.floor(Math.random() * 300) + 1}`,
    userName: `ARShopper${i + 1}`,
    deviceInfo: {
      model: devices[i % devices.length],
      os: osTypes[i % osTypes.length],
      arCapabilities: ['Object Detection', 'Surface Tracking', 'Hand Tracking', 'Face Tracking'].slice(0, Math.floor(Math.random() * 3) + 1),
      cameraResolution: ['1080p', '4K', '8K'][Math.floor(Math.random() * 3)],
      processingPower: Math.random() * 40 + 60 // 60-100%
    },
    sessionContext: {
      location: locations[i % locations.length],
      timeOfDay: timeOfDay[i % timeOfDay.length],
      duration: Math.random() * 1800 + 120, // 2-32 minutes
      lightingConditions: ['excellent', 'good', 'fair', 'poor'][Math.floor(Math.random() * 4)] as any,
      environmentType: Math.random() > 0.3 ? 'indoor' : 'outdoor' as any
    },
    shoppingJourney: {
      entryPoint: entryPoints[i % entryPoints.length],
      browsedCategories: categories.slice(0, Math.floor(Math.random() * 4) + 1),
      searchQueries: Array.from({ length: Math.floor(Math.random() * 5) + 1 }, (_, j) => `Search Query ${j + 1}`),
      productsViewed: Math.floor(Math.random() * 20) + 3,
      productsScanned: Math.floor(Math.random() * 8) + 1,
      comparisonsPerformed: Math.floor(Math.random() * 5),
      timeToFirstInteraction: Math.random() * 30 + 5 // 5-35 seconds
    },
    arInteractions: {
      objectRecognition: Math.floor(Math.random() * 50) + 10,
      surfaceDetection: Math.floor(Math.random() * 30) + 5,
      handGestures: Math.floor(Math.random() * 40) + 2,
      voiceCommands: Math.floor(Math.random() * 15),
      touchInteractions: Math.floor(Math.random() * 100) + 20,
      headMovements: Math.floor(Math.random() * 200) + 50,
      eyeTracking: Math.floor(Math.random() * 300) + 100,
      socialSharing: Math.floor(Math.random() * 3)
    },
    spatialBehavior: {
      roomScan: Math.random() > 0.4,
      placementAttempts: Math.floor(Math.random() * 8) + 1,
      placementSuccess: Math.floor(Math.random() * 6) + 1,
      movementRadius: Math.random() * 5 + 1, // 1-6 meters
      viewingAngles: Array.from({ length: 8 }, () => Math.random() * 360),
      distancePreference: Math.random() * 2 + 0.5, // 0.5-2.5 meters
      stationaryTime: Math.random() * 60 + 30,
      walkingTime: Math.random() * 120 + 10
    },
    productEngagement: {
      tryOnAttempts: Math.floor(Math.random() * 10) + 1,
      colorVariations: Math.floor(Math.random() * 8),
      sizeAdjustments: Math.floor(Math.random() * 6),
      customizations: Math.floor(Math.random() * 5),
      detailViews: Math.floor(Math.random() * 15) + 3,
      featureExploration: Math.floor(Math.random() * 12) + 2,
      socialValidation: Math.floor(Math.random() * 4),
      priceChecks: Math.floor(Math.random() * 6) + 1
    },
    purchaseDecision: {
      purchaseIntent: Math.random() * 100,
      addedToCart: Math.random() > 0.6,
      savedForLater: Math.random() > 0.5,
      sharedWithFriends: Math.random() > 0.7,
      requestedInfo: Math.random() > 0.8,
      abandonmentReason: Math.random() > 0.3 ? undefined : [
        'Technical issues', 'Price concerns', 'Need more time', 'Distracted by environment',
        'Battery low', 'Poor lighting', 'Privacy concerns', 'Not impressed with AR'
      ][Math.floor(Math.random() * 8)],
      conversionTime: Math.random() > 0.4 ? Math.random() * 300 + 60 : undefined
    },
    contextualFactors: {
      socialPresence: Math.random() > 0.6,
      backgroundNoise: Math.random() * 100,
      privacyLevel: Math.random() * 100,
      urgencyLevel: Math.random() * 100,
      moodIndicator: ['excited', 'curious', 'focused', 'relaxed', 'hurried', 'skeptical'][Math.floor(Math.random() * 6)],
      weatherCondition: Math.random() > 0.5 ? ['sunny', 'cloudy', 'rainy', 'snowy'][Math.floor(Math.random() * 4)] : undefined
    },
    technicalMetrics: {
      trackingStability: Math.random() * 30 + 70,
      renderingQuality: Math.random() * 25 + 75,
      latency: Math.random() * 100 + 20, // 20-120ms
      batteryImpact: Math.random() * 30 + 10, // 10-40%
      dataUsage: Math.random() * 100 + 20, // 20-120MB
      crashOccurrences: Math.random() > 0.9 ? Math.floor(Math.random() * 3) + 1 : 0
    },
    userFeedback: {
      usabilityRating: Math.random() * 3 + 7,
      accuracyRating: Math.random() * 2.5 + 7.5,
      enjoymentRating: Math.random() * 2 + 8,
      trustRating: Math.random() * 2.5 + 7.5,
      recommendationScore: Math.random() * 30 + 70
    },
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateARBehaviorMetrics = (): ARBehaviorMetric[] => {
  return Array.from({ length: 24 }, (_, i) => ({
    timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString().split('T')[1].split(':')[0] + ':00',
    activeSessions: Math.floor(Math.random() * 200) + 100,
    scanEvents: Math.floor(Math.random() * 500) + 200,
    placementSuccess: Math.random() * 30 + 70,
    averageEngagement: Math.random() * 40 + 60,
    conversionRate: Math.random() * 20 + 15,
    socialSharing: Math.floor(Math.random() * 50) + 20,
    returnUsers: Math.random() * 40 + 30,
    technicalQuality: Math.random() * 25 + 75,
    userSatisfaction: Math.random() * 20 + 80
  }));
};

const generateARLocationInsights = (): ARLocationInsight[] => {
  const locations = ['Home', 'Retail Store', 'Office', 'Outdoor', 'Public Transport', 'Social Space'];
  
  return locations.map((location, i) => ({
    location,
    sessionsCount: Math.floor(Math.random() * 1000) + 200,
    averageDuration: Math.random() * 600 + 180,
    conversionRate: Math.random() * 25 + 10,
    popularProducts: ['Fashion Items', 'Home Decor', 'Electronics', 'Beauty Products'].slice(0, Math.floor(Math.random() * 3) + 1),
    uniqueBehaviors: [
      ['Longer engagement times', 'Multiple try-ons', 'Social sharing'],
      ['Quick decisions', 'Price comparisons', 'In-store verification'],
      ['Professional focus', 'Discreet usage', 'Efficiency-driven'],
      ['Natural lighting advantage', 'Space constraints', 'Privacy concerns'],
      ['Time-constrained sessions', 'Noise interference', 'Battery awareness'],
      ['Group interactions', 'Social validation', 'Entertainment focus']
    ][i],
    technicalChallenges: [
      ['Variable lighting', 'Limited space'],
      ['Crowded environments', 'Network congestion'],
      ['Professional distractions', 'Corporate restrictions'],
      ['Lighting changes', 'Environmental motion'],
      ['Vibration interference', 'Network instability'],
      ['Background noise', 'Multiple users']
    ][i],
    successFactors: [
      ['Comfort and privacy', 'Unlimited time'],
      ['Product availability', 'Staff assistance'],
      ['Professional context', 'Quality devices'],
      ['Natural lighting', 'Open spaces'],
      ['Offline capabilities', 'Quick interactions'],
      ['Social features', 'Entertainment value']
    ][i],
    recommendations: [
      ['Optimize for home lighting conditions', 'Add sharing features'],
      ['Integrate with store inventory', 'Add staff notification features'],
      ['Create professional AR modes', 'Enhance efficiency features'],
      ['Improve tracking in variable conditions', 'Add weather awareness'],
      ['Optimize for mobile data', 'Add offline modes'],
      ['Enhance social features', 'Add group interaction modes']
    ][i]
  }));
};

const generateARProductCategories = (): ARProductCategory[] => {
  const categories = [
    {
      category: 'Fashion & Apparel',
      subcategories: ['Clothing', 'Shoes', 'Accessories', 'Jewelry'],
      technicalRequirements: {
        surfaceDetection: false,
        objectRecognition: true,
        handTracking: true,
        roomScale: false,
        lightingDependency: 'medium'
      }
    },
    {
      category: 'Home & Furniture',
      subcategories: ['Furniture', 'Decor', 'Appliances', 'Lighting'],
      technicalRequirements: {
        surfaceDetection: true,
        objectRecognition: true,
        handTracking: false,
        roomScale: true,
        lightingDependency: 'high'
      }
    },
    {
      category: 'Electronics',
      subcategories: ['Smartphones', 'Laptops', 'Audio', 'Gaming'],
      technicalRequirements: {
        surfaceDetection: true,
        objectRecognition: true,
        handTracking: true,
        roomScale: false,
        lightingDependency: 'low'
      }
    },
    {
      category: 'Beauty & Cosmetics',
      subcategories: ['Makeup', 'Skincare', 'Fragrance', 'Hair Care'],
      technicalRequirements: {
        surfaceDetection: false,
        objectRecognition: true,
        handTracking: true,
        roomScale: false,
        lightingDependency: 'high'
      }
    }
  ];

  return categories.map((cat, i) => ({
    ...cat,
    totalScans: Math.floor(Math.random() * 5000) + 1000,
    uniqueUsers: Math.floor(Math.random() * 2000) + 500,
    conversionRate: Math.random() * 25 + 15,
    averageEngagementTime: Math.random() * 300 + 120,
    popularFeatures: [
      ['Virtual Try-On', 'Size Fitting', 'Color Matching'],
      ['Room Placement', 'Scale Adjustment', 'Style Matching'],
      ['Feature Comparison', 'Size Visualization', 'Compatibility Check'],
      ['Virtual Makeup', 'Skin Tone Matching', 'Before/After Comparison']
    ][i],
    behaviorPatterns: {
      quickDeciders: Math.random() * 30 + 20,
      detailExplorers: Math.random() * 40 + 30,
      socialValidators: Math.random() * 25 + 15,
      priceComparers: Math.random() * 35 + 20
    }
  }));
};

const generateUserARProfiles = (): UserARProfile[] => {
  const ageGroups = ['18-24', '25-34', '35-44', '45-54', '55+'];
  const devicePreferences = ['iOS', 'Android', 'No Preference'];
  const shoppingFrequencies = ['Daily', 'Weekly', 'Monthly', 'Occasionally'];

  return Array.from({ length: 20 }, (_, i) => ({
    userId: `user-${i + 1}`,
    userName: `ARUser${i + 1}`,
    demographics: {
      ageGroup: ageGroups[i % ageGroups.length],
      techSavviness: Math.random() * 40 + 60,
      shoppingFrequency: shoppingFrequencies[i % shoppingFrequencies.length],
      devicePreference: devicePreferences[i % devicePreferences.length]
    },
    arExperience: {
      totalSessions: Math.floor(Math.random() * 100) + 10,
      totalTimeSpent: Math.random() * 5000 + 500, // minutes
      favoriteFeatures: ['Try-On', 'Room Placement', 'Social Sharing', 'Price Comparison'].slice(0, Math.floor(Math.random() * 3) + 1),
      expertiseLevel: ['beginner', 'intermediate', 'advanced', 'expert'][Math.floor(Math.random() * 4)] as any,
      preferredEnvironments: ['Home', 'Store', 'Office'].slice(0, Math.floor(Math.random() * 2) + 1)
    },
    shoppingBehavior: {
      averageSessionDuration: Math.random() * 600 + 180,
      conversionRate: Math.random() * 30 + 10,
      categoryPreferences: {
        'Fashion': Math.random() * 100,
        'Electronics': Math.random() * 100,
        'Home': Math.random() * 100,
        'Beauty': Math.random() * 100
      },
      decisionMakingSpeed: ['fast', 'moderate', 'slow', 'very_slow'][Math.floor(Math.random() * 4)] as any,
      socialInfluence: Math.random() * 100
    },
    technicalProfile: {
      deviceCapabilities: ['High-end Camera', 'Advanced Sensors', 'Fast Processor', '5G Connectivity'].slice(0, Math.floor(Math.random() * 3) + 1),
      networkPreference: ['5G', 'WiFi', 'LTE'][Math.floor(Math.random() * 3)],
      qualityPreference: ['performance', 'quality', 'battery'][Math.floor(Math.random() * 3)] as any,
      troubleshootingAbility: Math.random() * 100
    },
    engagementPatterns: {
      peakUsageHours: Array.from({ length: Math.floor(Math.random() * 4) + 2 }, () => Math.floor(Math.random() * 24)),
      sessionFrequency: ['Daily', 'Weekly', 'Monthly'][Math.floor(Math.random() * 3)],
      featureAdoption: {
        'BasicAR': true,
        'AdvancedTracking': Math.random() > 0.3,
        'SocialFeatures': Math.random() > 0.5,
        'VoiceCommands': Math.random() > 0.7
      },
      shareAndRecommend: Math.random() > 0.4
    }
  }));
};

const generateARShoppingInsights = (): ARShoppingInsight[] => {
  const categories = ['user_behavior', 'technology_adoption', 'conversion_optimization', 'location_impact', 'social_influence'] as const;
  const impacts = ['low', 'medium', 'high', 'critical'] as const;

  return Array.from({ length: 14 }, (_, i) => ({
    id: `ar-insight-${i + 1}`,
    category: categories[i % categories.length],
    insight: [
      'Users spend 60% more time exploring products in AR vs traditional browsing',
      'Hand gesture controls increase user engagement by 40% over touch interfaces',
      'AR sessions in retail stores have 85% higher conversion rates than at home',
      'Social sharing from AR experiences drives 3x more organic app downloads',
      'Voice commands reduce session abandonment by 25% in noisy environments',
      'Room-scale AR for furniture increases purchase confidence by 70%',
      'AR makeup try-on reduces product returns by 50%',
      'Weekend AR shopping sessions are 2x longer than weekday sessions',
      'Lighting quality directly correlates with 65% of AR session satisfaction',
      'Multi-user AR experiences boost group purchase decisions by 80%',
      'AR product comparisons lead to 45% higher average order values',
      'Mobile AR performance impacts user retention more than feature richness',
      'Contextual AR recommendations based on location increase engagement by 55%',
      'AR onboarding tutorials improve feature adoption rates by 90%'
    ][i],
    dataPoints: Math.floor(Math.random() * 5000) + 1000,
    confidence: Math.random() * 25 + 75,
    impact: impacts[i % impacts.length],
    recommendation: [
      'Implement enhanced product visualization and extended AR sessions',
      'Prioritize gesture-based navigation over traditional touch controls',
      'Partner with retailers to create immersive in-store AR experiences',
      'Add one-click social sharing with AR content generation',
      'Integrate voice commands with environmental noise adaptation',
      'Expand room-scale AR features for home and furniture categories',
      'Invest in advanced facial recognition and skin tone matching',
      'Create weekend-specific AR shopping campaigns and features',
      'Implement automatic lighting adjustment and user guidance',
      'Develop collaborative AR shopping features for groups',
      'Add side-by-side AR comparison tools with smart suggestions',
      'Optimize AR performance before adding new features',
      'Use location data to provide contextually relevant AR experiences',
      'Create interactive AR tutorials for each major feature'
    ][i],
    implementation: {
      complexity: ['simple', 'moderate', 'complex'][Math.floor(Math.random() * 3)] as any,
      timeframe: ['2-4 weeks', '1-2 months', '2-3 months', '3-6 months'][Math.floor(Math.random() * 4)],
      resources: ['AR Developers', 'UX Designers', 'Data Scientists', 'Product Managers', 'QA Engineers'].slice(0, Math.floor(Math.random() * 3) + 2),
      expectedOutcome: [
        '60% increase in user engagement and session duration',
        '40% improvement in user interaction satisfaction',
        '85% boost in retail partnership conversion rates',
        '200% increase in organic user acquisition',
        '25% reduction in session abandonment rates',
        '70% improvement in furniture purchase confidence',
        '50% decrease in beauty product return rates',
        '100% increase in weekend session engagement',
        '65% improvement in overall user satisfaction',
        '80% increase in group purchase conversion',
        '45% growth in average order values',
        '30% improvement in user retention rates',
        '55% boost in location-based engagement',
        '90% increase in advanced feature adoption'
      ][i]
    },
    affectedSegments: [
      ['All Users', 'Product Browsers', 'Long-session Users'],
      ['Tech-savvy Users', 'Frequent Shoppers', 'Mobile Users'],
      ['In-store Shoppers', 'Retail Partners', 'Local Users'],
      ['Social Users', 'Young Demographics', 'Influencers'],
      ['Commuters', 'Outdoor Users', 'Voice-enabled Devices'],
      ['Home Buyers', 'Interior Designers', 'Family Shoppers'],
      ['Beauty Enthusiasts', 'Fashion-forward Users', 'Return-sensitive Buyers'],
      ['Weekend Shoppers', 'Leisure Users', 'Family Buyers'],
      ['All Users', 'Quality-conscious Shoppers', 'Premium Segment'],
      ['Group Shoppers', 'Family Buyers', 'Social Influencers'],
      ['Comparison Shoppers', 'High-value Customers', 'Research-oriented Users'],
      ['Performance-sensitive Users', 'Budget Device Users', 'Retention-focused Segment'],
      ['Location-aware Users', 'Local Shoppers', 'Context-sensitive Segment'],
      ['New Users', 'Feature Explorers', 'Tutorial-responsive Segment']
    ][i],
    businessValue: [
      '$2.5M annual revenue increase from longer engagement',
      '$1.8M savings from improved user experience',
      '$5M potential from retail partnerships',
      '$3.2M in reduced acquisition costs',
      '$800K savings from reduced support tickets',
      '$4.1M revenue increase in furniture category',
      '$2.7M savings from reduced returns',
      '$1.5M revenue boost from weekend campaigns',
      '$2.1M value from improved satisfaction scores',
      '$3.8M from group purchase conversions',
      '$6.2M from higher order values',
      '$4.5M from improved retention rates',
      '$2.9M from location-based features',
      '$1.6M from feature adoption improvements'
    ][i],
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const ARShoppingBehavior: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [locationFilter, setLocationFilter] = useState<string>('all');
  const [deviceFilter, setDeviceFilter] = useState<string>('all');
  const [activeTab, setActiveTab] = useState('overview');

  // Generate mock data
  const arSessions = useMemo(() => generateARShoppingSessions(), []);
  const behaviorMetrics = useMemo(() => generateARBehaviorMetrics(), []);
  const locationInsights = useMemo(() => generateARLocationInsights(), []);
  const productCategories = useMemo(() => generateARProductCategories(), []);
  const userProfiles = useMemo(() => generateUserARProfiles(), []);
  const shoppingInsights = useMemo(() => generateARShoppingInsights(), []);

  // Filter and search logic
  const filteredSessions = useMemo(() => {
    return arSessions.filter(session => {
      const matchesSearch = session.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          session.deviceInfo.model.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesLocation = locationFilter === 'all' || session.sessionContext.location === locationFilter;
      const matchesDevice = deviceFilter === 'all' || session.deviceInfo.model.includes(deviceFilter);
      return matchesSearch && matchesLocation && matchesDevice;
    });
  }, [arSessions, searchTerm, locationFilter, deviceFilter]);

  const getLocationIcon = (location: string) => {
    switch (location) {
      case 'home': return <Home className="h-4 w-4" />;
      case 'store': return <Store className="h-4 w-4" />;
      case 'office': return <Building2 className="h-4 w-4" />;
      case 'outdoor': return <TreePine className="h-4 w-4" />;
      case 'transport': return <Car className="h-4 w-4" />;
      case 'social': return <Coffee className="h-4 w-4" />;
      default: return <MapPin className="h-4 w-4" />;
    }
  };

  const getLightingColor = (lighting: string) => {
    switch (lighting) {
      case 'excellent': return 'text-green-600';
      case 'good': return 'text-blue-600';
      case 'fair': return 'text-yellow-600';
      case 'poor': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getQualityColor = (value: number, type: 'percentage' | 'latency' | 'rating') => {
    if (type === 'percentage') {
      return value >= 80 ? 'text-green-600' : value >= 60 ? 'text-yellow-600' : 'text-red-600';
    } else if (type === 'latency') {
      return value <= 50 ? 'text-green-600' : value <= 100 ? 'text-yellow-600' : 'text-red-600';
    } else { // rating
      return value >= 8 ? 'text-green-600' : value >= 6 ? 'text-yellow-600' : 'text-red-600';
    }
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="p-6 space-y-6 bg-gradient-to-br from-rose-50 via-orange-50 to-amber-50 min-h-screen">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-rose-600 to-orange-600 bg-clip-text text-transparent">
            AR Shopping Behavior Analysis
          </h1>
          <p className="text-gray-600 mt-2">Advanced analytics for augmented reality shopping patterns and consumer behavior</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Refresh Data
          </Button>
          <Button className="flex items-center gap-2 bg-gradient-to-r from-rose-500 to-orange-500">
            <Scan className="h-4 w-4" />
            AR Analytics
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sessions">AR Sessions</TabsTrigger>
          <TabsTrigger value="locations">Location Insights</TabsTrigger>
          <TabsTrigger value="categories">Product Categories</TabsTrigger>
          <TabsTrigger value="users">User Profiles</TabsTrigger>
          <TabsTrigger value="behavior">Behavior Patterns</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="bg-gradient-to-br from-rose-500 to-rose-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-rose-100 text-sm font-medium">AR Sessions</p>
                    <p className="text-3xl font-bold">{arSessions.length}</p>
                  </div>
                  <Scan className="h-8 w-8 text-rose-200" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="bg-rose-400 text-rose-900">
                    +{Math.floor(Math.random() * 30 + 15)}% vs last week
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-orange-100 text-sm font-medium">Avg Session Time</p>
                    <p className="text-3xl font-bold">
                      {formatDuration(arSessions.reduce((sum, s) => sum + s.sessionContext.duration, 0) / arSessions.length)}
                    </p>
                  </div>
                  <Timer className="h-8 w-8 text-orange-200" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="bg-orange-400 text-orange-900">
                    AR Immersion
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-amber-500 to-amber-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-amber-100 text-sm font-medium">Conversion Rate</p>
                    <p className="text-3xl font-bold">
                      {Math.floor(arSessions.filter(s => s.purchaseDecision.addedToCart).length / arSessions.length * 100)}%
                    </p>
                  </div>
                  <ShoppingCart className="h-8 w-8 text-amber-200" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="bg-amber-400 text-amber-900">
                    AR Commerce
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-emerald-500 to-emerald-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-emerald-100 text-sm font-medium">Satisfaction Score</p>
                    <p className="text-3xl font-bold">
                      {(arSessions.reduce((sum, s) => sum + s.userFeedback.usabilityRating, 0) / arSessions.length).toFixed(1)}
                    </p>
                  </div>
                  <Star className="h-8 w-8 text-emerald-200" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="bg-emerald-400 text-emerald-900">
                    /10 Rating
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  AR Engagement Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={behaviorMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="activeSessions" stroke="#f43f5e" fill="#f43f5e" fillOpacity={0.6} />
                    <Area type="monotone" dataKey="averageEngagement" stroke="#fb7185" fill="#fb7185" fillOpacity={0.4} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  AR Performance Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsLineChart data={behaviorMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="conversionRate" stroke="#10b981" strokeWidth={2} />
                    <Line type="monotone" dataKey="placementSuccess" stroke="#f59e0b" strokeWidth={2} />
                    <Line type="monotone" dataKey="technicalQuality" stroke="#8b5cf6" strokeWidth={2} />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Scan className="h-5 w-5" />
                  AR Interaction Types
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsBarChart data={behaviorMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="scanEvents" fill="#f43f5e" />
                    <Bar dataKey="socialSharing" fill="#fb923c" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Location Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {locationInsights.slice(0, 6).map((location) => (
                    <div key={location.location} className="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg">
                      <div className="flex items-center gap-3">
                        {getLocationIcon(location.location.toLowerCase())}
                        <div>
                          <p className="font-medium">{location.location}</p>
                          <p className="text-sm text-gray-600">{location.sessionsCount} sessions</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-green-600">{location.conversionRate.toFixed(1)}%</p>
                        <p className="text-sm text-gray-600">Conversion</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="sessions" className="space-y-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search AR sessions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={locationFilter}
                onChange={(e) => setLocationFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Locations</option>
                <option value="home">Home</option>
                <option value="store">Store</option>
                <option value="office">Office</option>
                <option value="outdoor">Outdoor</option>
                <option value="transport">Transport</option>
                <option value="social">Social</option>
              </select>
              <select
                value={deviceFilter}
                onChange={(e) => setDeviceFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Devices</option>
                <option value="iPhone">iPhone</option>
                <option value="Galaxy">Galaxy</option>
                <option value="Pixel">Pixel</option>
                <option value="OnePlus">OnePlus</option>
              </select>
            </div>
          </div>

          <div className="grid gap-4">
            <AnimatePresence>
              {filteredSessions.map((session) => (
                <motion.div
                  key={session.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card className="hover:shadow-lg transition-shadow bg-gradient-to-r from-white to-gray-50">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <div className="w-10 h-10 bg-gradient-to-br from-rose-400 to-orange-400 rounded-full flex items-center justify-center text-white font-bold">
                              {session.userName.slice(0, 2).toUpperCase()}
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold">{session.userName}</h3>
                              <p className="text-sm text-gray-600">{session.deviceInfo.model} • {session.deviceInfo.os}</p>
                            </div>
                            {getLocationIcon(session.sessionContext.location)}
                            <Badge variant="outline" className="capitalize">
                              {session.sessionContext.location}
                            </Badge>
                            <Badge variant="secondary">
                              {formatDuration(session.sessionContext.duration)}
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 text-sm">
                            <div>
                              <p className="text-gray-600">Shopping Journey</p>
                              <p className="font-semibold">{session.shoppingJourney.productsViewed} viewed</p>
                              <p className="text-xs text-gray-500">
                                {session.shoppingJourney.productsScanned} scanned, {session.shoppingJourney.comparisonsPerformed} compared
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-600">AR Interactions</p>
                              <p className="font-semibold">{session.arInteractions.touchInteractions + session.arInteractions.handGestures}</p>
                              <p className="text-xs text-gray-500">
                                {session.arInteractions.handGestures} gestures, {session.arInteractions.voiceCommands} voice
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-600">Product Engagement</p>
                              <p className="font-semibold">{session.productEngagement.tryOnAttempts} try-ons</p>
                              <p className="text-xs text-gray-500">
                                {session.productEngagement.colorVariations} colors, {session.productEngagement.sizeAdjustments} sizes
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-600">Outcome</p>
                              <p className={`font-semibold ${session.purchaseDecision.addedToCart ? 'text-green-600' : 'text-gray-600'}`}>
                                {session.purchaseDecision.addedToCart ? 'Converted' : 'Browsed'}
                              </p>
                              <p className="text-xs text-gray-500">
                                {Math.floor(session.purchaseDecision.purchaseIntent)}% intent
                              </p>
                            </div>
                          </div>

                          <div className="bg-gradient-to-r from-rose-50 to-orange-50 p-4 rounded-lg mb-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                              <div>
                                <p className="font-medium text-rose-700">Context</p>
                                <p className={`${getLightingColor(session.sessionContext.lightingConditions)} capitalize`}>
                                  {session.sessionContext.lightingConditions} lighting
                                </p>
                                <p className="text-xs text-gray-600 capitalize">
                                  {session.sessionContext.timeOfDay} • {session.sessionContext.environmentType}
                                </p>
                              </div>
                              <div>
                                <p className="font-medium text-orange-700">Technical Quality</p>
                                <p className={`${getQualityColor(session.technicalMetrics.trackingStability, 'percentage')}`}>
                                  {Math.floor(session.technicalMetrics.trackingStability)}% tracking
                                </p>
                                <p className={`text-xs ${getQualityColor(session.technicalMetrics.latency, 'latency')}`}>
                                  {Math.floor(session.technicalMetrics.latency)}ms latency
                                </p>
                              </div>
                              <div>
                                <p className="font-medium text-amber-700">User Feedback</p>
                                <p className={`${getQualityColor(session.userFeedback.usabilityRating, 'rating')}`}>
                                  {session.userFeedback.usabilityRating.toFixed(1)}/10 usability
                                </p>
                                <p className={`text-xs ${getQualityColor(session.userFeedback.enjoymentRating, 'rating')}`}>
                                  {session.userFeedback.enjoymentRating.toFixed(1)}/10 enjoyment
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm font-medium text-gray-700 mb-2">Spatial Behavior:</p>
                              <div className="flex flex-wrap gap-1">
                                {session.spatialBehavior.roomScan && (
                                  <Badge variant="secondary" className="text-xs">Room Scanned</Badge>
                                )}
                                <Badge variant="secondary" className="text-xs">
                                  {session.spatialBehavior.placementAttempts} placements
                                </Badge>
                                <Badge variant="secondary" className="text-xs">
                                  {session.spatialBehavior.distancePreference.toFixed(1)}m distance
                                </Badge>
                              </div>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-700 mb-2">Contextual Factors:</p>
                              <div className="flex flex-wrap gap-1">
                                {session.contextualFactors.socialPresence && (
                                  <Badge variant="secondary" className="text-xs">Social Present</Badge>
                                )}
                                <Badge variant="secondary" className="text-xs capitalize">
                                  {session.contextualFactors.moodIndicator} mood
                                </Badge>
                                {session.contextualFactors.weatherCondition && (
                                  <Badge variant="secondary" className="text-xs capitalize">
                                    {session.contextualFactors.weatherCondition}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col gap-2">
                          <Button size="sm" variant="outline" className="flex items-center gap-1">
                            <Play className="h-4 w-4" />
                            Replay AR
                          </Button>
                          <Button size="sm" variant="outline" className="flex items-center gap-1">
                            <BarChart3 className="h-4 w-4" />
                            Analytics
                          </Button>
                          <Button size="sm" variant="outline">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </TabsContent>

        <TabsContent value="locations" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Location-Based AR Insights</h2>
            <Button className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Location Analytics
            </Button>
          </div>

          <div className="grid gap-6">
            {locationInsights.map((location) => (
              <Card key={location.location} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {getLocationIcon(location.location.toLowerCase().replace(' ', '_'))}
                    {location.location}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div className="bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-blue-700 mb-2">Session Metrics</h4>
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <span>Sessions:</span>
                          <span className="font-medium">{location.sessionsCount}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Avg Duration:</span>
                          <span className="font-medium">{formatDuration(location.averageDuration)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Conversion:</span>
                          <span className="font-medium text-green-600">{location.conversionRate.toFixed(1)}%</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-green-700 mb-2">Popular Products</h4>
                      <div className="space-y-1">
                        {location.popularProducts.map((product, idx) => (
                          <div key={idx} className="text-xs">{product}</div>
                        ))}
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-purple-700 mb-2">Unique Behaviors</h4>
                      <div className="space-y-1">
                        {location.uniqueBehaviors.map((behavior, idx) => (
                          <div key={idx} className="text-xs">{behavior}</div>
                        ))}
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-orange-50 to-yellow-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-orange-700 mb-2">Success Factors</h4>
                      <div className="space-y-1">
                        {location.successFactors.map((factor, idx) => (
                          <div key={idx} className="text-xs">{factor}</div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold text-gray-700 mb-3">Technical Challenges</h4>
                      <div className="space-y-2">
                        {location.technicalChallenges.map((challenge, idx) => (
                          <div key={idx} className="flex items-center gap-2 text-sm">
                            <AlertTriangle className="h-4 w-4 text-red-500" />
                            <span>{challenge}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold text-gray-700 mb-3">Recommendations</h4>
                      <div className="space-y-2">
                        {location.recommendations.map((recommendation, idx) => (
                          <div key={idx} className="flex items-center gap-2 text-sm">
                            <Lightbulb className="h-4 w-4 text-green-500" />
                            <span>{recommendation}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">AR Product Categories</h2>
            <Button variant="outline" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Category Filters
            </Button>
          </div>

          <div className="grid gap-6">
            {productCategories.map((category) => (
              <Card key={category.category} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Box className="h-5 w-5" />
                    {category.category}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div className="bg-gradient-to-r from-cyan-50 to-blue-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-cyan-700 mb-2">Usage Statistics</h4>
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <span>Total Scans:</span>
                          <span className="font-medium">{category.totalScans.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Unique Users:</span>
                          <span className="font-medium">{category.uniqueUsers.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Conversion:</span>
                          <span className="font-medium text-green-600">{category.conversionRate.toFixed(1)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Avg Time:</span>
                          <span className="font-medium">{formatDuration(category.averageEngagementTime)}</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-green-700 mb-2">Popular Features</h4>
                      <div className="space-y-1">
                        {category.popularFeatures.map((feature, idx) => (
                          <div key={idx} className="text-xs">{feature}</div>
                        ))}
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-purple-700 mb-2">Behavior Patterns</h4>
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <span className="text-xs">Quick Deciders:</span>
                          <span className="text-xs font-medium">{category.behaviorPatterns.quickDeciders.toFixed(1)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-xs">Detail Explorers:</span>
                          <span className="text-xs font-medium">{category.behaviorPatterns.detailExplorers.toFixed(1)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-xs">Social Validators:</span>
                          <span className="text-xs font-medium">{category.behaviorPatterns.socialValidators.toFixed(1)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-xs">Price Comparers:</span>
                          <span className="text-xs font-medium">{category.behaviorPatterns.priceComparers.toFixed(1)}%</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-orange-50 to-yellow-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-orange-700 mb-2">Technical Needs</h4>
                      <div className="space-y-1 text-xs">
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${category.technicalRequirements.surfaceDetection ? 'bg-green-500' : 'bg-gray-400'}`} />
                          <span>Surface Detection</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${category.technicalRequirements.objectRecognition ? 'bg-green-500' : 'bg-gray-400'}`} />
                          <span>Object Recognition</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${category.technicalRequirements.handTracking ? 'bg-green-500' : 'bg-gray-400'}`} />
                          <span>Hand Tracking</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${category.technicalRequirements.roomScale ? 'bg-green-500' : 'bg-gray-400'}`} />
                          <span>Room Scale</span>
                        </div>
                        <div className="text-xs text-gray-600 mt-1">
                          Lighting: {category.technicalRequirements.lightingDependency}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-700 mb-3">Subcategories</h4>
                    <div className="flex flex-wrap gap-2">
                      {category.subcategories.map((sub, idx) => (
                        <Badge key={idx} variant="secondary">
                          {sub}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">AR User Profiles</h2>
            <Button className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              User Insights
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {userProfiles.slice(0, 12).map((profile) => (
              <Card key={profile.userId} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{profile.userName}</CardTitle>
                    <Badge variant="outline" className="capitalize">
                      {profile.arExperience.expertiseLevel}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">{profile.demographics.ageGroup}</Badge>
                    <Badge variant="secondary">{profile.demographics.devicePreference}</Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Total Sessions</p>
                      <p className="font-semibold">{profile.arExperience.totalSessions}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Total Time</p>
                      <p className="font-semibold">{Math.floor(profile.arExperience.totalTimeSpent / 60)}h</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Conversion Rate</p>
                      <p className="font-semibold text-green-600">{profile.shoppingBehavior.conversionRate.toFixed(1)}%</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Tech Savviness</p>
                      <p className="font-semibold">{Math.floor(profile.demographics.techSavviness)}%</p>
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">Favorite Features:</p>
                    <div className="flex flex-wrap gap-1">
                      {profile.arExperience.favoriteFeatures.map((feature, idx) => (
                        <Badge key={idx} variant="secondary" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">Preferred Environments:</p>
                    <div className="flex flex-wrap gap-1">
                      {profile.arExperience.preferredEnvironments.map((env, idx) => (
                        <Badge key={idx} variant="outline" className="text-xs">
                          {env}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-3 rounded-lg">
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div>
                        <p className="text-gray-600">Decision Speed</p>
                        <p className="font-medium capitalize">{profile.shoppingBehavior.decisionMakingSpeed.replace('_', ' ')}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Social Influence</p>
                        <p className="font-medium">{Math.floor(profile.shoppingBehavior.socialInfluence)}%</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Session Frequency</p>
                        <p className="font-medium">{profile.engagementPatterns.sessionFrequency}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Quality Preference</p>
                        <p className="font-medium capitalize">{profile.technicalProfile.qualityPreference}</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="h-4 w-4 mr-1" />
                      Profile
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <BarChart3 className="h-4 w-4 mr-1" />
                      Journey
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="behavior" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  AR Interaction Patterns
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RadarChart data={[
                    {
                      interaction: 'Touch',
                      value: arSessions.reduce((sum, s) => sum + s.arInteractions.touchInteractions, 0) / arSessions.length / 100 * 100
                    },
                    {
                      interaction: 'Gestures',
                      value: arSessions.reduce((sum, s) => sum + s.arInteractions.handGestures, 0) / arSessions.length / 40 * 100
                    },
                    {
                      interaction: 'Voice',
                      value: arSessions.reduce((sum, s) => sum + s.arInteractions.voiceCommands, 0) / arSessions.length / 15 * 100
                    },
                    {
                      interaction: 'Eye Tracking',
                      value: arSessions.reduce((sum, s) => sum + s.arInteractions.eyeTracking, 0) / arSessions.length / 300 * 100
                    },
                    {
                      interaction: 'Object Recognition',
                      value: arSessions.reduce((sum, s) => sum + s.arInteractions.objectRecognition, 0) / arSessions.length / 50 * 100
                    },
                    {
                      interaction: 'Surface Detection',
                      value: arSessions.reduce((sum, s) => sum + s.arInteractions.surfaceDetection, 0) / arSessions.length / 30 * 100
                    }
                  ]}>
                    <PolarGrid />
                    <PolarAngleAxis dataKey="interaction" />
                    <PolarRadiusAxis angle={90} domain={[0, 100]} />
                    <Radar dataKey="value" stroke="#f43f5e" fill="#f43f5e" fillOpacity={0.6} />
                  </RadarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Shopping Journey Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsBarChart data={behaviorMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="scanEvents" fill="#f43f5e" />
                    <Bar dataKey="conversionRate" fill="#10b981" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Engagement vs Performance Correlation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <ScatterChart data={arSessions.map(s => ({
                  engagement: s.productEngagement.tryOnAttempts + s.productEngagement.detailViews,
                  satisfaction: s.userFeedback.usabilityRating,
                  conversion: s.purchaseDecision.addedToCart ? 100 : 0
                }))}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="engagement" name="Engagement Level" />
                  <YAxis dataKey="satisfaction" name="Satisfaction" />
                  <Tooltip cursor={{ strokeDasharray: '3 3' }} />
                  <Scatter dataKey="conversion" fill="#f43f5e" />
                </ScatterChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">AR Shopping Insights</h2>
            <Button className="flex items-center gap-2 bg-gradient-to-r from-rose-500 to-orange-500">
              <Brain className="h-4 w-4" />
              Generate Insights
            </Button>
          </div>

          <div className="grid gap-4">
            {shoppingInsights.map((insight) => (
              <Card key={insight.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <Badge variant="outline" className="capitalize">
                          {insight.category.replace('_', ' ')}
                        </Badge>
                        <Badge variant={
                          insight.impact === 'critical' ? 'destructive' :
                          insight.impact === 'high' ? 'default' :
                          insight.impact === 'medium' ? 'secondary' :
                          'outline'
                        }>
                          {insight.impact} impact
                        </Badge>
                        <Badge variant="outline">{Math.floor(insight.confidence)}% confidence</Badge>
                        <Badge variant="outline" className="border-rose-500 text-rose-700">
                          {insight.dataPoints.toLocaleString()} data points
                        </Badge>
                      </div>
                      <h3 className="text-lg font-semibold mb-2">{insight.insight}</h3>
                      <p className="text-gray-600 mb-4">{insight.recommendation}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <h4 className="font-semibold text-gray-700 mb-2">Implementation</h4>
                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span>Complexity:</span>
                              <span className={`font-medium capitalize ${
                                insight.implementation.complexity === 'complex' ? 'text-red-600' :
                                insight.implementation.complexity === 'moderate' ? 'text-yellow-600' :
                                'text-green-600'
                              }`}>
                                {insight.implementation.complexity}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Timeframe:</span>
                              <span className="font-medium">{insight.implementation.timeframe}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Business Value:</span>
                              <span className="font-medium text-green-600">{insight.businessValue}</span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-semibold text-gray-700 mb-2">Affected Segments</h4>
                          <div className="flex flex-wrap gap-1">
                            {insight.affectedSegments.map((segment, idx) => (
                              <Badge key={idx} variant="secondary" className="text-xs">
                                {segment}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>

                      <div className="bg-gradient-to-r from-rose-50 to-orange-50 p-4 rounded-lg">
                        <h4 className="font-semibold text-rose-700 mb-2">Expected Outcome</h4>
                        <p className="text-sm text-gray-700">{insight.implementation.expectedOutcome}</p>
                        <div className="mt-2">
                          <p className="text-xs text-gray-600">
                            Resources: {insight.implementation.resources.join(', ')}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button size="sm" className="flex items-center gap-1">
                        <CheckCircle className="h-4 w-4" />
                        Implement
                      </Button>
                      <Button variant="outline" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ARShoppingBehavior;