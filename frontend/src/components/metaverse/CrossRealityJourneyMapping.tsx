/**
 * Cross-Reality Customer Journey Mapping
 * Advanced analytics for omnichannel customer experiences across physical, digital, and virtual environments
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Route,
  Navigation,
  Compass,
  MapPin,
  Globe,
  Monitor,
  Smartphone,
  Tablet,
  Watch,
  Glasses,
  Headphones,
  Gamepad2,
  Eye,
  Activity,
  Users,
  Clock,
  Target,
  TrendingUp,
  TrendingDown,
  BarChart3,
  LineChart,
  PieChart,
  Layers,
  Move3d,
  ArrowRight,
  ArrowLeft,
  ArrowUp,
  ArrowDown,
  ChevronRight,
  Play,
  Pause,
  Stop,
  Rewind,
  FastForward,
  SkipBack,
  SkipForward,
  Home,
  Store,
  Building2,
  Car,
  Plane,
  Coffee,
  ShoppingCart,
  ShoppingBag,
  CreditCard,
  Wallet,
  DollarSign,
  Star,
  Heart,
  ThumbsUp,
  Share,
  MessageCircle,
  Phone,
  Mail,
  Bell,
  Search,
  Filter,
  Settings,
  RefreshCw,
  Download,
  Upload,
  ExternalLink,
  Link2,
  Network,
  Wifi,
  Signal,
  Battery,
  Power,
  Zap,
  Brain,
  Sparkles,
  Wand2,
  Magic,
  Crosshair,
  Focus,
  Scan,
  Camera,
  Video,
  Image,
  Music,
  Volume2,
  Mic,
  Speaker,
  Hand,
  Fingerprint,
  Shield,
  Lock,
  Key,
  Unlock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info,
  HelpCircle,
  BookOpen,
  FileText,
  File,
  Folder,
  Archive,
  Save,
  Edit,
  Trash,
  Plus,
  Minus,
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  Calendar,
  Lightbulb,
  Rocket,
  Award,
  Trophy,
  Crown,
  Gem,
  Box,
  Cube,
  Palette,
  Brush,
  Gauge
} from 'lucide-react';
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart as RechartsBarChart, Bar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ScatterChart, Scatter, ComposedChart, Cell, Sankey, Treemap, PieChart as RechartsPieChart, Pie } from 'recharts';

// TypeScript interfaces for cross-reality journey mapping
interface RealityChannel {
  id: string;
  name: string;
  type: 'physical' | 'digital' | 'virtual' | 'augmented' | 'mixed';
  platform: string;
  deviceTypes: string[];
  capabilities: {
    sensoryInputs: string[];
    interactionMethods: string[];
    dataCollection: string[];
    personalization: number;
    immersionLevel: number;
    socialFeatures: boolean;
  };
  technicalSpecs: {
    resolution: string;
    frameRate: number;
    latency: number;
    bandwidth: string;
    storageRequirements: string;
    processingPower: string;
  };
  businessMetrics: {
    customerSatisfaction: number;
    conversionRate: number;
    engagementTime: number;
    retentionRate: number;
    operationalCosts: number;
    maintenanceComplexity: number;
  };
  integrationComplexity: number;
  accessibilityScore: number;
  privacyLevel: number;
  securityRating: number;
}

interface CustomerTouchpoint {
  id: string;
  name: string;
  channelId: string;
  touchpointType: 'awareness' | 'consideration' | 'purchase' | 'support' | 'retention' | 'advocacy';
  customerActions: string[];
  businessActions: string[];
  dataCollected: string[];
  emotionalState: {
    satisfaction: number;
    frustration: number;
    excitement: number;
    confusion: number;
    trust: number;
    urgency: number;
  };
  technicalMetrics: {
    loadTime: number;
    errorRate: number;
    completionRate: number;
    dropoffRate: number;
    accessibilityScore: number;
  };
  personalization: {
    contentCustomization: number;
    behaviorAdaptation: number;
    contextAwareness: number;
    predictiveCapability: number;
  };
  integrationPoints: string[];
  optimizationOpportunities: string[];
}

interface CrossRealityJourney {
  id: string;
  customerId: string;
  customerSegment: string;
  journeyType: 'discovery' | 'purchase' | 'support' | 'exploration' | 'social' | 'learning';
  startTime: string;
  endTime: string;
  totalDuration: number;
  touchpoints: {
    touchpointId: string;
    channelId: string;
    sequenceOrder: number;
    duration: number;
    transitionTime: number;
    transitionMethod: string;
    contextData: Record<string, any>;
    deviceContext: {
      deviceType: string;
      location: string;
      networkCondition: string;
      batteryLevel: number;
      screenSize: string;
      inputMethod: string;
    };
    interactionData: {
      clicks: number;
      gestures: number;
      voiceCommands: number;
      gazeTracking: number;
      movementData: Record<string, number>;
      biometricData: Record<string, number>;
    };
    emotionalJourney: {
      entryEmotion: string;
      exitEmotion: string;
      emotionalArc: { timestamp: string; emotion: string; intensity: number }[];
      satisfactionScore: number;
      effortScore: number;
    };
    businessOutcome: {
      goalAchieved: boolean;
      conversionEvent: boolean;
      revenueGenerated: number;
      costsIncurred: number;
      valueCreated: number;
    };
  }[];
  journeyMetrics: {
    completionRate: number;
    satisfactionScore: number;
    effortScore: number;
    emotionalScore: number;
    technicalpPerformance: number;
    businessValue: number;
  };
  crossChannelSynergies: {
    dataConsistency: number;
    contextContinuity: number;
    personalizationCoherence: number;
    brandConsistency: number;
  };
  optimizationOpportunities: {
    frictionPoints: string[];
    enhancementOpportunities: string[];
    crossChannelGaps: string[];
    technologyUpgrades: string[];
  };
  predictedNextActions: {
    action: string;
    probability: number;
    channel: string;
    timeframe: string;
  }[];
}

interface JourneyAnalytics {
  timeframe: string;
  totalJourneys: number;
  uniqueCustomers: number;
  averageJourneyDuration: number;
  completionRate: number;
  satisfactionScore: number;
  channelDistribution: { channelId: string; usage: number; performance: number }[];
  touchpointPerformance: { touchpointId: string; satisfaction: number; completion: number; efficiency: number }[];
  crossChannelTransitions: { from: string; to: string; frequency: number; successRate: number; averageTime: number }[];
  emotionalJourneyPatterns: { emotion: string; frequency: number; impact: number; triggers: string[] }[];
  conversionFunnels: {
    stage: string;
    entries: number;
    exits: number;
    conversionRate: number;
    channels: { channelId: string; contribution: number }[];
  }[];
  cohortAnalysis: {
    cohort: string;
    journeyCompletion: number;
    satisfaction: number;
    revenuePerJourney: number;
    preferredChannels: string[];
  }[];
  deviceContextAnalysis: {
    deviceType: string;
    usage: number;
    performance: number;
    satisfaction: number;
    conversionRate: number;
  }[];
  realTimeMetrics: {
    activeJourneys: number;
    channelLoad: { channelId: string; currentLoad: number; capacity: number }[];
    systemPerformance: { metric: string; current: number; target: number; status: string }[];
    alertsActive: { type: string; severity: string; message: string; timestamp: string }[];
  };
}

interface JourneyOptimization {
  id: string;
  category: 'friction_reduction' | 'experience_enhancement' | 'cross_channel_integration' | 'personalization_improvement' | 'technology_upgrade';
  title: string;
  description: string;
  affectedTouchpoints: string[];
  affectedChannels: string[];
  currentState: {
    metrics: { metric: string; current: number; benchmark: number }[];
    painPoints: string[];
    customerFeedback: string[];
  };
  proposedSolution: {
    changes: string[];
    implementation: {
      phases: { phase: string; duration: string; requirements: string[] }[];
      resources: string[];
      technologies: string[];
      costs: { category: string; amount: number }[];
    };
    expectedOutcomes: {
      metrics: { metric: string; expected: number; improvement: number }[];
      businessValue: string;
      customerValue: string[];
      riskFactors: string[];
    };
  };
  priorityScore: number;
  feasibilityScore: number;
  impactScore: number;
  roiProjection: number;
  implementationComplexity: 'low' | 'medium' | 'high' | 'very_high';
  createdAt: string;
}

interface ChannelIntegration {
  fromChannelId: string;
  toChannelId: string;
  integrationType: 'data_sync' | 'context_transfer' | 'experience_continuity' | 'identity_bridging' | 'real_time_sync';
  integrationMethods: string[];
  dataFlowDirection: 'unidirectional' | 'bidirectional' | 'hub_spoke' | 'mesh';
  latency: number;
  reliability: number;
  dataConsistency: number;
  securityLevel: number;
  implementationStatus: 'planned' | 'in_progress' | 'implemented' | 'optimizing';
  businessValue: number;
  technicalComplexity: number;
  maintenanceRequirement: number;
  scalabilityScore: number;
}

// Mock data generation functions
const generateRealityChannels = (): RealityChannel[] => {
  const channels = [
    {
      name: 'Physical Store',
      type: 'physical' as const,
      platform: 'Retail Location',
      deviceTypes: ['Smart Displays', 'POS Systems', 'Mobile Scanners', 'Interactive Kiosks']
    },
    {
      name: 'E-commerce Website',
      type: 'digital' as const,
      platform: 'Web Browser',
      deviceTypes: ['Desktop', 'Laptop', 'Tablet', 'Mobile']
    },
    {
      name: 'Mobile App',
      type: 'digital' as const,
      platform: 'iOS/Android',
      deviceTypes: ['Smartphone', 'Tablet']
    },
    {
      name: 'VR Showroom',
      type: 'virtual' as const,
      platform: 'VR Platform',
      deviceTypes: ['VR Headset', 'Hand Controllers', 'Haptic Devices']
    },
    {
      name: 'AR Try-On',
      type: 'augmented' as const,
      platform: 'AR Framework',
      deviceTypes: ['Smartphone', 'AR Glasses', 'Tablet']
    },
    {
      name: 'Social VR Space',
      type: 'virtual' as const,
      platform: 'Metaverse Platform',
      deviceTypes: ['VR Headset', 'Avatar Controllers', 'Spatial Audio']
    },
    {
      name: 'Smart Mirror',
      type: 'mixed' as const,
      platform: 'IoT Display',
      deviceTypes: ['Interactive Mirror', 'Gesture Sensors', 'Camera']
    },
    {
      name: 'Voice Assistant',
      type: 'digital' as const,
      platform: 'AI Platform',
      deviceTypes: ['Smart Speaker', 'Smartphone', 'Smartwatch']
    }
  ];

  return channels.map((channel, i) => ({
    id: `channel-${i + 1}`,
    ...channel,
    capabilities: {
      sensoryInputs: ['Visual', 'Audio', 'Touch', 'Gesture', 'Voice'].slice(0, Math.floor(Math.random() * 4) + 2),
      interactionMethods: ['Touch', 'Voice', 'Gesture', 'Gaze', 'Motion'].slice(0, Math.floor(Math.random() * 4) + 1),
      dataCollection: ['Behavioral', 'Biometric', 'Contextual', 'Preference', 'Performance'].slice(0, Math.floor(Math.random() * 4) + 2),
      personalization: Math.random() * 100,
      immersionLevel: Math.random() * 100,
      socialFeatures: Math.random() > 0.5
    },
    technicalSpecs: {
      resolution: ['4K', '8K', '1080p', '720p'][Math.floor(Math.random() * 4)],
      frameRate: [30, 60, 90, 120][Math.floor(Math.random() * 4)],
      latency: Math.random() * 100 + 10,
      bandwidth: ['1 Mbps', '10 Mbps', '100 Mbps', '1 Gbps'][Math.floor(Math.random() * 4)],
      storageRequirements: ['1 GB', '10 GB', '100 GB', '1 TB'][Math.floor(Math.random() * 4)],
      processingPower: ['Low', 'Medium', 'High', 'Very High'][Math.floor(Math.random() * 4)]
    },
    businessMetrics: {
      customerSatisfaction: Math.random() * 30 + 70,
      conversionRate: Math.random() * 25 + 5,
      engagementTime: Math.random() * 300 + 60,
      retentionRate: Math.random() * 40 + 60,
      operationalCosts: Math.random() * 100000 + 10000,
      maintenanceComplexity: Math.random() * 100
    },
    integrationComplexity: Math.random() * 100,
    accessibilityScore: Math.random() * 100,
    privacyLevel: Math.random() * 100,
    securityRating: Math.random() * 100
  }));
};

const generateCustomerTouchpoints = (channels: RealityChannel[]): CustomerTouchpoint[] => {
  const touchpointTypes = ['awareness', 'consideration', 'purchase', 'support', 'retention', 'advocacy'] as const;
  const touchpointNames = [
    'Product Discovery', 'Feature Exploration', 'Price Comparison', 'Reviews Reading', 'Virtual Try-On',
    'Add to Cart', 'Checkout Process', 'Payment Processing', 'Order Confirmation', 'Shipping Tracking',
    'Product Delivery', 'Unboxing Experience', 'First Use', 'Customer Support', 'Product Review',
    'Social Sharing', 'Repeat Purchase', 'Loyalty Program', 'Referral Program', 'Feedback Collection'
  ];

  return Array.from({ length: 20 }, (_, i) => ({
    id: `touchpoint-${i + 1}`,
    name: touchpointNames[i],
    channelId: channels[i % channels.length].id,
    touchpointType: touchpointTypes[i % touchpointTypes.length],
    customerActions: ['Browse', 'Search', 'Compare', 'Select', 'Purchase', 'Share', 'Review'].slice(0, Math.floor(Math.random() * 4) + 2),
    businessActions: ['Display', 'Recommend', 'Personalize', 'Assist', 'Process', 'Follow-up'].slice(0, Math.floor(Math.random() * 3) + 2),
    dataCollected: ['Behavior', 'Preferences', 'Context', 'Feedback', 'Performance'].slice(0, Math.floor(Math.random() * 4) + 1),
    emotionalState: {
      satisfaction: Math.random() * 100,
      frustration: Math.random() * 50,
      excitement: Math.random() * 100,
      confusion: Math.random() * 40,
      trust: Math.random() * 100,
      urgency: Math.random() * 100
    },
    technicalMetrics: {
      loadTime: Math.random() * 5 + 0.5,
      errorRate: Math.random() * 5,
      completionRate: Math.random() * 30 + 70,
      dropoffRate: Math.random() * 20,
      accessibilityScore: Math.random() * 100
    },
    personalization: {
      contentCustomization: Math.random() * 100,
      behaviorAdaptation: Math.random() * 100,
      contextAwareness: Math.random() * 100,
      predictiveCapability: Math.random() * 100
    },
    integrationPoints: channels.slice(0, Math.floor(Math.random() * 3) + 1).map(c => c.id),
    optimizationOpportunities: [
      'Reduce loading time', 'Improve personalization', 'Enhanced accessibility',
      'Better error handling', 'Streamlined process', 'Cross-channel integration'
    ].slice(0, Math.floor(Math.random() * 3) + 1)
  }));
};

const generateCrossRealityJourneys = (channels: RealityChannel[], touchpoints: CustomerTouchpoint[]): CrossRealityJourney[] => {
  const journeyTypes = ['discovery', 'purchase', 'support', 'exploration', 'social', 'learning'] as const;
  const customerSegments = ['New Customer', 'Returning Customer', 'VIP Customer', 'Business Customer', 'Student'];

  return Array.from({ length: 12 }, (_, i) => {
    const touchpointCount = Math.floor(Math.random() * 8) + 3;
    const selectedTouchpoints = touchpoints
      .sort(() => Math.random() - 0.5)
      .slice(0, touchpointCount);

    return {
      id: `journey-${i + 1}`,
      customerId: `customer-${Math.floor(Math.random() * 1000) + 1}`,
      customerSegment: customerSegments[i % customerSegments.length],
      journeyType: journeyTypes[i % journeyTypes.length],
      startTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      endTime: new Date(Date.now() - Math.random() * 6 * 24 * 60 * 60 * 1000).toISOString(),
      totalDuration: Math.random() * 2400 + 300, // 5-45 minutes
      touchpoints: selectedTouchpoints.map((tp, index) => ({
        touchpointId: tp.id,
        channelId: tp.channelId,
        sequenceOrder: index + 1,
        duration: Math.random() * 300 + 30,
        transitionTime: Math.random() * 60 + 5,
        transitionMethod: ['Direct Link', 'QR Code', 'Voice Command', 'Gesture', 'Automatic'][Math.floor(Math.random() * 5)],
        contextData: {
          location: ['Home', 'Office', 'Store', 'Commute'][Math.floor(Math.random() * 4)],
          timeOfDay: ['Morning', 'Afternoon', 'Evening', 'Night'][Math.floor(Math.random() * 4)],
          socialContext: ['Alone', 'With Friends', 'With Family', 'In Group'][Math.floor(Math.random() * 4)]
        },
        deviceContext: {
          deviceType: ['Desktop', 'Mobile', 'Tablet', 'VR Headset', 'AR Glasses'][Math.floor(Math.random() * 5)],
          location: ['Indoor', 'Outdoor', 'Vehicle', 'Public Space'][Math.floor(Math.random() * 4)],
          networkCondition: ['Excellent', 'Good', 'Fair', 'Poor'][Math.floor(Math.random() * 4)],
          batteryLevel: Math.random() * 100,
          screenSize: ['Small', 'Medium', 'Large', 'Extra Large'][Math.floor(Math.random() * 4)],
          inputMethod: ['Touch', 'Voice', 'Gesture', 'Gaze', 'Controller'][Math.floor(Math.random() * 5)]
        },
        interactionData: {
          clicks: Math.floor(Math.random() * 50),
          gestures: Math.floor(Math.random() * 30),
          voiceCommands: Math.floor(Math.random() * 10),
          gazeTracking: Math.floor(Math.random() * 100),
          movementData: {
            distance: Math.random() * 100,
            speed: Math.random() * 10,
            acceleration: Math.random() * 5
          },
          biometricData: {
            heartRate: Math.random() * 40 + 60,
            stressLevel: Math.random() * 100,
            attention: Math.random() * 100
          }
        },
        emotionalJourney: {
          entryEmotion: ['Curious', 'Excited', 'Neutral', 'Frustrated', 'Confident'][Math.floor(Math.random() * 5)],
          exitEmotion: ['Satisfied', 'Delighted', 'Neutral', 'Disappointed', 'Confused'][Math.floor(Math.random() * 5)],
          emotionalArc: Array.from({ length: 5 }, (_, j) => ({
            timestamp: new Date(Date.now() - (4 - j) * 60000).toISOString(),
            emotion: ['Happy', 'Neutral', 'Frustrated', 'Excited', 'Confused'][Math.floor(Math.random() * 5)],
            intensity: Math.random() * 100
          })),
          satisfactionScore: Math.random() * 100,
          effortScore: Math.random() * 100
        },
        businessOutcome: {
          goalAchieved: Math.random() > 0.3,
          conversionEvent: Math.random() > 0.7,
          revenueGenerated: Math.random() * 1000,
          costsIncurred: Math.random() * 50,
          valueCreated: Math.random() * 500
        }
      })),
      journeyMetrics: {
        completionRate: Math.random() * 30 + 70,
        satisfactionScore: Math.random() * 30 + 70,
        effortScore: Math.random() * 40 + 60,
        emotionalScore: Math.random() * 30 + 70,
        technicalpPerformance: Math.random() * 30 + 70,
        businessValue: Math.random() * 1000 + 100
      },
      crossChannelSynergies: {
        dataConsistency: Math.random() * 30 + 70,
        contextContinuity: Math.random() * 30 + 70,
        personalizationCoherence: Math.random() * 30 + 70,
        brandConsistency: Math.random() * 30 + 70
      },
      optimizationOpportunities: {
        frictionPoints: ['Slow loading times', 'Inconsistent data', 'Poor transitions', 'Device limitations'].slice(0, Math.floor(Math.random() * 3) + 1),
        enhancementOpportunities: ['Better personalization', 'Smoother transitions', 'Enhanced features', 'Improved accessibility'].slice(0, Math.floor(Math.random() * 3) + 1),
        crossChannelGaps: ['Data synchronization', 'Context transfer', 'Identity bridging', 'Experience continuity'].slice(0, Math.floor(Math.random() * 2) + 1),
        technologyUpgrades: ['Faster processors', 'Better sensors', 'Improved networks', 'Enhanced displays'].slice(0, Math.floor(Math.random() * 2) + 1)
      },
      predictedNextActions: [
        { action: 'Make Purchase', probability: Math.random() * 100, channel: 'E-commerce', timeframe: '24 hours' },
        { action: 'Read Reviews', probability: Math.random() * 100, channel: 'Mobile App', timeframe: '1 hour' },
        { action: 'Share Product', probability: Math.random() * 100, channel: 'Social Media', timeframe: '30 minutes' }
      ]
    };
  });
};

const generateJourneyAnalytics = (channels: RealityChannel[], journeys: CrossRealityJourney[]): JourneyAnalytics => {
  return {
    timeframe: 'Last 30 Days',
    totalJourneys: journeys.length * 10,
    uniqueCustomers: Math.floor(journeys.length * 7),
    averageJourneyDuration: journeys.reduce((sum, j) => sum + j.totalDuration, 0) / journeys.length,
    completionRate: journeys.reduce((sum, j) => sum + j.journeyMetrics.completionRate, 0) / journeys.length,
    satisfactionScore: journeys.reduce((sum, j) => sum + j.journeyMetrics.satisfactionScore, 0) / journeys.length,
    channelDistribution: channels.map(channel => ({
      channelId: channel.id,
      usage: Math.random() * 100,
      performance: Math.random() * 100
    })),
    touchpointPerformance: Array.from({ length: 10 }, (_, i) => ({
      touchpointId: `touchpoint-${i + 1}`,
      satisfaction: Math.random() * 30 + 70,
      completion: Math.random() * 30 + 70,
      efficiency: Math.random() * 30 + 70
    })),
    crossChannelTransitions: [
      { from: 'Physical Store', to: 'Mobile App', frequency: 450, successRate: 87, averageTime: 120 },
      { from: 'E-commerce Website', to: 'AR Try-On', frequency: 320, successRate: 92, averageTime: 45 },
      { from: 'Mobile App', to: 'VR Showroom', frequency: 180, successRate: 78, averageTime: 90 },
      { from: 'VR Showroom', to: 'Physical Store', frequency: 150, successRate: 85, averageTime: 300 },
      { from: 'AR Try-On', to: 'E-commerce Website', frequency: 280, successRate: 94, averageTime: 30 }
    ],
    emotionalJourneyPatterns: [
      { emotion: 'Excited', frequency: 35, impact: 85, triggers: ['New Product', 'Discount', 'Personalization'] },
      { emotion: 'Frustrated', frequency: 15, impact: -60, triggers: ['Slow Loading', 'Error', 'Poor UX'] },
      { emotion: 'Satisfied', frequency: 40, impact: 75, triggers: ['Goal Achievement', 'Good Service', 'Easy Process'] },
      { emotion: 'Confused', frequency: 10, impact: -45, triggers: ['Complex Interface', 'Too Many Options', 'Poor Navigation'] }
    ],
    conversionFunnels: [
      {
        stage: 'Awareness',
        entries: 10000,
        exits: 3000,
        conversionRate: 70,
        channels: [
          { channelId: 'channel-1', contribution: 40 },
          { channelId: 'channel-2', contribution: 35 },
          { channelId: 'channel-3', contribution: 25 }
        ]
      },
      {
        stage: 'Consideration',
        entries: 7000,
        exits: 2000,
        conversionRate: 71,
        channels: [
          { channelId: 'channel-2', contribution: 45 },
          { channelId: 'channel-4', contribution: 30 },
          { channelId: 'channel-5', contribution: 25 }
        ]
      },
      {
        stage: 'Purchase',
        entries: 5000,
        exits: 1500,
        conversionRate: 70,
        channels: [
          { channelId: 'channel-2', contribution: 50 },
          { channelId: 'channel-1', contribution: 30 },
          { channelId: 'channel-3', contribution: 20 }
        ]
      }
    ],
    cohortAnalysis: [
      { cohort: 'New Customers', journeyCompletion: 65, satisfaction: 78, revenuePerJourney: 120, preferredChannels: ['Mobile App', 'E-commerce'] },
      { cohort: 'Returning Customers', journeyCompletion: 82, satisfaction: 85, revenuePerJourney: 180, preferredChannels: ['Physical Store', 'VR Showroom'] },
      { cohort: 'VIP Customers', journeyCompletion: 91, satisfaction: 92, revenuePerJourney: 350, preferredChannels: ['VR Showroom', 'Personal Assistant'] },
      { cohort: 'Business Customers', journeyCompletion: 88, satisfaction: 89, revenuePerJourney: 450, preferredChannels: ['B2B Portal', 'Video Conferencing'] }
    ],
    deviceContextAnalysis: [
      { deviceType: 'Mobile', usage: 45, performance: 82, satisfaction: 79, conversionRate: 18 },
      { deviceType: 'Desktop', usage: 25, performance: 91, satisfaction: 87, conversionRate: 24 },
      { deviceType: 'Tablet', usage: 15, performance: 85, satisfaction: 83, conversionRate: 21 },
      { deviceType: 'VR Headset', usage: 10, performance: 78, satisfaction: 89, conversionRate: 32 },
      { deviceType: 'AR Glasses', usage: 5, performance: 75, satisfaction: 85, conversionRate: 28 }
    ],
    realTimeMetrics: {
      activeJourneys: 1247,
      channelLoad: channels.map(channel => ({
        channelId: channel.id,
        currentLoad: Math.random() * 100,
        capacity: 100
      })),
      systemPerformance: [
        { metric: 'Response Time', current: 150, target: 200, status: 'Good' },
        { metric: 'Uptime', current: 99.8, target: 99.5, status: 'Excellent' },
        { metric: 'Error Rate', current: 0.2, target: 0.5, status: 'Good' },
        { metric: 'Satisfaction', current: 85, target: 80, status: 'Excellent' }
      ],
      alertsActive: [
        { type: 'Performance', severity: 'Medium', message: 'VR Showroom latency above threshold', timestamp: '2 minutes ago' },
        { type: 'Integration', severity: 'Low', message: 'Data sync delay between mobile and web', timestamp: '5 minutes ago' }
      ]
    }
  };
};

const generateJourneyOptimizations = (): JourneyOptimization[] => {
  const categories = ['friction_reduction', 'experience_enhancement', 'cross_channel_integration', 'personalization_improvement', 'technology_upgrade'] as const;
  
  return Array.from({ length: 8 }, (_, i) => ({
    id: `optimization-${i + 1}`,
    category: categories[i % categories.length],
    title: [
      'Reduce VR-to-Mobile Transition Friction by 40%',
      'Enhance Personalization Across All Touchpoints',
      'Implement Real-Time Cross-Channel Data Sync',
      'Improve AR Try-On Accuracy and Performance',
      'Streamline Multi-Device Authentication Flow',
      'Optimize Physical-to-Digital Experience Handoff',
      'Deploy AI-Powered Emotion Recognition',
      'Upgrade Network Infrastructure for Better Latency'
    ][i],
    description: [
      'Customers experience significant delays and data loss when transitioning from VR showroom to mobile purchase flow',
      'Personalization is inconsistent across channels, leading to disjointed user experiences and reduced engagement',
      'Data synchronization delays between channels cause confusion and duplicate actions for customers',
      'AR try-on feature has accuracy issues that impact customer confidence and conversion rates',
      'Multi-device authentication creates friction points that increase abandonment rates',
      'The transition from physical store browsing to digital purchase lacks context preservation',
      'Implement emotion recognition to better understand customer journey sentiment and optimize accordingly',
      'Network latency issues in VR and AR experiences impact user satisfaction and engagement'
    ][i],
    affectedTouchpoints: [`touchpoint-${(i % 5) + 1}`, `touchpoint-${(i % 7) + 2}`],
    affectedChannels: [`channel-${(i % 4) + 1}`, `channel-${(i % 6) + 2}`],
    currentState: {
      metrics: [
        { metric: 'Transition Success Rate', current: Math.random() * 30 + 50, benchmark: Math.random() * 20 + 80 },
        { metric: 'Customer Satisfaction', current: Math.random() * 20 + 60, benchmark: Math.random() * 15 + 85 },
        { metric: 'Completion Rate', current: Math.random() * 25 + 65, benchmark: Math.random() * 15 + 85 }
      ],
      painPoints: [
        'Long loading times during transitions',
        'Loss of context between channels',
        'Inconsistent user interfaces',
        'Authentication friction',
        'Data synchronization delays'
      ].slice(0, Math.floor(Math.random() * 3) + 2),
      customerFeedback: [
        'Frustrating to restart process on different device',
        'Wish my preferences carried over between apps',
        'Takes too long to authenticate on new device',
        'Lost my shopping cart when switching channels'
      ].slice(0, Math.floor(Math.random() * 2) + 1)
    },
    proposedSolution: {
      changes: [
        'Implement seamless state transfer between channels',
        'Deploy unified authentication system',
        'Optimize data synchronization infrastructure',
        'Enhance cross-platform UI consistency',
        'Add predictive pre-loading capabilities'
      ].slice(0, Math.floor(Math.random() * 3) + 2),
      implementation: {
        phases: [
          { phase: 'Analysis & Design', duration: '4 weeks', requirements: ['UX Research', 'Technical Architecture', 'System Analysis'] },
          { phase: 'Development', duration: '8 weeks', requirements: ['Engineering Team', 'QA Testing', 'Security Review'] },
          { phase: 'Deployment & Optimization', duration: '2 weeks', requirements: ['DevOps', 'Monitoring Setup', 'Performance Tuning'] }
        ],
        resources: ['Development Team', 'UX Designers', 'DevOps Engineers', 'QA Testers'].slice(0, Math.floor(Math.random() * 3) + 2),
        technologies: ['Real-time Sync', 'Cloud Infrastructure', 'AI/ML', 'Edge Computing'].slice(0, Math.floor(Math.random() * 3) + 1),
        costs: [
          { category: 'Development', amount: Math.floor(Math.random() * 100000) + 50000 },
          { category: 'Infrastructure', amount: Math.floor(Math.random() * 50000) + 20000 },
          { category: 'Testing', amount: Math.floor(Math.random() * 30000) + 10000 }
        ]
      },
      expectedOutcomes: {
        metrics: [
          { metric: 'Transition Success Rate', expected: Math.random() * 20 + 85, improvement: Math.random() * 30 + 15 },
          { metric: 'Customer Satisfaction', expected: Math.random() * 15 + 85, improvement: Math.random() * 25 + 10 },
          { metric: 'Completion Rate', expected: Math.random() * 15 + 85, improvement: Math.random() * 20 + 10 }
        ],
        businessValue: `$${Math.floor(Math.random() * 500000) + 200000} annual revenue increase`,
        customerValue: [
          'Seamless experience across all channels',
          'Reduced friction and effort',
          'Consistent personalization',
          'Faster task completion'
        ].slice(0, Math.floor(Math.random() * 3) + 2),
        riskFactors: [
          'Technical complexity',
          'Integration challenges',
          'User adoption time',
          'Performance impact'
        ].slice(0, Math.floor(Math.random() * 2) + 1)
      }
    },
    priorityScore: Math.random() * 40 + 60,
    feasibilityScore: Math.random() * 40 + 60,
    impactScore: Math.random() * 40 + 60,
    roiProjection: Math.random() * 300 + 150,
    implementationComplexity: ['low', 'medium', 'high', 'very_high'][Math.floor(Math.random() * 4)] as any,
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const CrossRealityJourneyMapping: React.FC = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('30d');
  const [selectedChannel, setSelectedChannel] = useState('all');
  const [selectedJourneyType, setSelectedJourneyType] = useState('all');
  const [viewMode, setViewMode] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');

  // Generated data
  const realityChannels = useMemo(() => generateRealityChannels(), []);
  const customerTouchpoints = useMemo(() => generateCustomerTouchpoints(realityChannels), [realityChannels]);
  const crossRealityJourneys = useMemo(() => generateCrossRealityJourneys(realityChannels, customerTouchpoints), [realityChannels, customerTouchpoints]);
  const journeyAnalytics = useMemo(() => generateJourneyAnalytics(realityChannels, crossRealityJourneys), [realityChannels, crossRealityJourneys]);
  const journeyOptimizations = useMemo(() => generateJourneyOptimizations(), []);

  // Filtering and processing
  const filteredOptimizations = useMemo(() => {
    return journeyOptimizations.filter(opt => 
      opt.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      opt.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      opt.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [journeyOptimizations, searchTerm]);

  const filteredJourneys = useMemo(() => {
    return crossRealityJourneys.filter(journey => 
      (selectedJourneyType === 'all' || journey.journeyType === selectedJourneyType) &&
      (selectedChannel === 'all' || journey.touchpoints.some(tp => tp.channelId === selectedChannel))
    );
  }, [crossRealityJourneys, selectedJourneyType, selectedChannel]);

  const channelPerformanceData = useMemo(() => {
    return realityChannels.map(channel => ({
      name: channel.name,
      satisfaction: channel.businessMetrics.customerSatisfaction,
      conversion: channel.businessMetrics.conversionRate,
      engagement: channel.businessMetrics.engagementTime / 60, // Convert to minutes
      retention: channel.businessMetrics.retentionRate
    }));
  }, [realityChannels]);

  const getChannelIcon = (type: string) => {
    switch (type) {
      case 'physical': return <Store className="h-4 w-4" />;
      case 'digital': return <Monitor className="h-4 w-4" />;
      case 'virtual': return <Glasses className="h-4 w-4" />;
      case 'augmented': return <Scan className="h-4 w-4" />;
      case 'mixed': return <Layers className="h-4 w-4" />;
      default: return <Globe className="h-4 w-4" />;
    }
  };

  const getOptimizationIcon = (category: string) => {
    switch (category) {
      case 'friction_reduction': return <Zap className="h-4 w-4" />;
      case 'experience_enhancement': return <Star className="h-4 w-4" />;
      case 'cross_channel_integration': return <Network className="h-4 w-4" />;
      case 'personalization_improvement': return <Target className="h-4 w-4" />;
      case 'technology_upgrade': return <Rocket className="h-4 w-4" />;
      default: return <Lightbulb className="h-4 w-4" />;
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'very_high': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="w-full space-y-6 p-6 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <Route className="h-8 w-8 text-blue-600" />
            Cross-Reality Journey Mapping
          </h1>
          <p className="text-gray-600 mt-1">Omnichannel customer experiences across physical, digital, and virtual environments</p>
        </div>
        
        <div className="flex flex-wrap items-center gap-3">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search optimizations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
            />
          </div>
          
          <select
            value={selectedChannel}
            onChange={(e) => setSelectedChannel(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Channels</option>
            {realityChannels.map(channel => (
              <option key={channel.id} value={channel.id}>{channel.name}</option>
            ))}
          </select>
          
          <select
            value={selectedJourneyType}
            onChange={(e) => setSelectedJourneyType(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Journey Types</option>
            <option value="discovery">Discovery</option>
            <option value="purchase">Purchase</option>
            <option value="support">Support</option>
            <option value="exploration">Exploration</option>
            <option value="social">Social</option>
            <option value="learning">Learning</option>
          </select>
          
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">Total Journeys</p>
                <p className="text-2xl font-bold">{journeyAnalytics.totalJourneys.toLocaleString()}</p>
                <p className="text-blue-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +14.3% vs last period
                </p>
              </div>
              <Route className="h-8 w-8 text-blue-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm">Completion Rate</p>
                <p className="text-2xl font-bold">{journeyAnalytics.completionRate.toFixed(1)}%</p>
                <p className="text-green-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +3.7% vs last period
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm">Satisfaction</p>
                <p className="text-2xl font-bold">{journeyAnalytics.satisfactionScore.toFixed(1)}%</p>
                <p className="text-purple-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +2.1% vs last period
                </p>
              </div>
              <Heart className="h-8 w-8 text-purple-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm">Avg Duration</p>
                <p className="text-2xl font-bold">{Math.round(journeyAnalytics.averageJourneyDuration / 60)}m</p>
                <p className="text-orange-100 text-xs flex items-center mt-1">
                  <TrendingDown className="h-3 w-3 mr-1" />
                  -8.2% vs last period
                </p>
              </div>
              <Clock className="h-8 w-8 text-orange-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-teal-500 to-teal-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-teal-100 text-sm">Active Journeys</p>
                <p className="text-2xl font-bold">{journeyAnalytics.realTimeMetrics.activeJourneys.toLocaleString()}</p>
                <p className="text-teal-100 text-xs flex items-center mt-1">
                  <Activity className="h-3 w-3 mr-1" />
                  Real-time
                </p>
              </div>
              <Users className="h-8 w-8 text-teal-200" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={viewMode} onValueChange={setViewMode} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="channels">Channels</TabsTrigger>
          <TabsTrigger value="journeys">Journeys</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="optimizations">Optimizations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Channel Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Channel Performance Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RadarChart data={channelPerformanceData}>
                    <PolarGrid />
                    <PolarAngleAxis dataKey="name" />
                    <PolarRadiusAxis angle={30} />
                    <Radar name="Satisfaction" dataKey="satisfaction" stroke="#8B5CF6" fill="#8B5CF6" fillOpacity={0.3} />
                    <Radar name="Conversion" dataKey="conversion" stroke="#10B981" fill="#10B981" fillOpacity={0.3} />
                    <Tooltip />
                  </RadarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Cross-Channel Transitions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Network className="h-5 w-5" />
                  Top Cross-Channel Transitions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {journeyAnalytics.crossChannelTransitions.map((transition, i) => (
                    <div key={i} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">{transition.from}</span>
                          <ArrowRight className="h-4 w-4 text-gray-400" />
                          <span className="text-sm font-medium">{transition.to}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-bold">{transition.frequency}</p>
                        <p className="text-xs text-gray-500">{transition.successRate}% success</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Emotional Journey Patterns */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Heart className="h-5 w-5" />
                Emotional Journey Patterns
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <ComposedChart data={journeyAnalytics.emotionalJourneyPatterns}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="emotion" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Bar yAxisId="left" dataKey="frequency" fill="#3B82F6" />
                  <Line yAxisId="right" type="monotone" dataKey="impact" stroke="#EF4444" strokeWidth={2} />
                </ComposedChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="channels" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {realityChannels.map((channel) => (
              <Card key={channel.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="flex items-center gap-2">
                      {getChannelIcon(channel.type)}
                      {channel.name}
                    </CardTitle>
                    <Badge variant="outline" className="capitalize">
                      {channel.type}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">{channel.platform}</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Business Metrics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Performance Metrics</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="bg-blue-50 p-2 rounded text-center">
                        <p className="font-bold text-blue-600">{channel.businessMetrics.customerSatisfaction.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Satisfaction</p>
                      </div>
                      <div className="bg-green-50 p-2 rounded text-center">
                        <p className="font-bold text-green-600">{channel.businessMetrics.conversionRate.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Conversion</p>
                      </div>
                      <div className="bg-purple-50 p-2 rounded text-center">
                        <p className="font-bold text-purple-600">{Math.round(channel.businessMetrics.engagementTime / 60)}m</p>
                        <p className="text-xs text-gray-600">Engagement</p>
                      </div>
                      <div className="bg-orange-50 p-2 rounded text-center">
                        <p className="font-bold text-orange-600">{channel.businessMetrics.retentionRate.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Retention</p>
                      </div>
                    </div>
                  </div>

                  {/* Capabilities */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Capabilities</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Immersion Level:</span>
                        <span className="font-medium">{channel.capabilities.immersionLevel.toFixed(0)}%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Personalization:</span>
                        <span className="font-medium">{channel.capabilities.personalization.toFixed(0)}%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Social Features:</span>
                        <span className="font-medium">{channel.capabilities.socialFeatures ? 'Yes' : 'No'}</span>
                      </div>
                    </div>
                  </div>

                  {/* Technical Specs */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Technical Details</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Resolution:</span>
                        <span className="font-medium">{channel.technicalSpecs.resolution}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Frame Rate:</span>
                        <span className="font-medium">{channel.technicalSpecs.frameRate} FPS</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Latency:</span>
                        <span className="font-medium">{channel.technicalSpecs.latency.toFixed(0)}ms</span>
                      </div>
                    </div>
                  </div>

                  {/* Interaction Methods */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Interaction Methods</h4>
                    <div className="flex flex-wrap gap-1">
                      {channel.capabilities.interactionMethods.map((method, i) => (
                        <Badge key={i} variant="secondary" className="text-xs">{method}</Badge>
                      ))}
                    </div>
                  </div>

                  <Button variant="outline" className="w-full">
                    <Eye className="h-4 w-4 mr-2" />
                    View Channel Details
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="journeys" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredJourneys.slice(0, 6).map((journey) => (
              <Card key={journey.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Route className="h-5 w-5" />
                      Journey {journey.id.split('-')[1]}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="capitalize">
                        {journey.journeyType}
                      </Badge>
                      <Badge variant="secondary">
                        {journey.customerSegment}
                      </Badge>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">
                    Duration: {Math.round(journey.totalDuration / 60)} minutes | {journey.touchpoints.length} touchpoints
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Journey Metrics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Journey Performance</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="bg-green-50 p-2 rounded text-center">
                        <p className="font-bold text-green-600">{journey.journeyMetrics.completionRate.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Completion</p>
                      </div>
                      <div className="bg-blue-50 p-2 rounded text-center">
                        <p className="font-bold text-blue-600">{journey.journeyMetrics.satisfactionScore.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Satisfaction</p>
                      </div>
                      <div className="bg-purple-50 p-2 rounded text-center">
                        <p className="font-bold text-purple-600">{journey.journeyMetrics.effortScore.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Effort</p>
                      </div>
                      <div className="bg-orange-50 p-2 rounded text-center">
                        <p className="font-bold text-orange-600">${journey.journeyMetrics.businessValue.toFixed(0)}</p>
                        <p className="text-xs text-gray-600">Value</p>
                      </div>
                    </div>
                  </div>

                  {/* Touchpoint Flow */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Touchpoint Flow</h4>
                    <div className="flex items-center gap-2 overflow-x-auto pb-2">
                      {journey.touchpoints.slice(0, 5).map((touchpoint, i) => (
                        <React.Fragment key={i}>
                          <div className="flex flex-col items-center min-w-0 flex-shrink-0">
                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">
                              {i + 1}
                            </div>
                            <span className="text-xs text-gray-600 mt-1 text-center max-w-16 truncate">
                              {realityChannels.find(c => c.id === touchpoint.channelId)?.name || 'Channel'}
                            </span>
                          </div>
                          {i < Math.min(journey.touchpoints.length - 1, 4) && (
                            <ArrowRight className="h-4 w-4 text-gray-400 flex-shrink-0" />
                          )}
                        </React.Fragment>
                      ))}
                      {journey.touchpoints.length > 5 && (
                        <span className="text-xs text-gray-500 flex-shrink-0">+{journey.touchpoints.length - 5} more</span>
                      )}
                    </div>
                  </div>

                  {/* Cross-Channel Synergies */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Cross-Channel Synergies</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Data Consistency:</span>
                        <span className="font-medium">{journey.crossChannelSynergies.dataConsistency.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Context Continuity:</span>
                        <span className="font-medium">{journey.crossChannelSynergies.contextContinuity.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Brand Consistency:</span>
                        <span className="font-medium">{journey.crossChannelSynergies.brandConsistency.toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>

                  {/* Predicted Next Actions */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Predicted Next Actions</h4>
                    <div className="space-y-1">
                      {journey.predictedNextActions.slice(0, 2).map((action, i) => (
                        <div key={i} className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">{action.action}</span>
                          <span className="font-medium">{action.probability.toFixed(0)}%</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Button variant="outline" size="sm" className="w-full">
                    <Eye className="h-4 w-4 mr-2" />
                    View Journey Details
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Conversion Funnel */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Cross-Reality Conversion Funnel
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsBarChart data={journeyAnalytics.conversionFunnels}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="stage" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="entries" fill="#3B82F6" />
                    <Bar dataKey="exits" fill="#EF4444" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Device Context Analysis */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Monitor className="h-5 w-5" />
                  Device Context Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ComposedChart data={journeyAnalytics.deviceContextAnalysis}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="deviceType" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Bar yAxisId="left" dataKey="usage" fill="#8B5CF6" />
                    <Line yAxisId="right" type="monotone" dataKey="satisfaction" stroke="#10B981" strokeWidth={2} />
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Cohort Analysis */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Customer Cohort Journey Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {journeyAnalytics.cohortAnalysis.map((cohort, i) => (
                  <div key={i} className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-3">{cohort.cohort}</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Completion:</span>
                        <span className="font-medium">{cohort.journeyCompletion}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Satisfaction:</span>
                        <span className="font-medium">{cohort.satisfaction}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Revenue/Journey:</span>
                        <span className="font-medium">${cohort.revenuePerJourney}</span>
                      </div>
                      <div className="mt-2">
                        <span className="text-gray-600 text-xs">Preferred Channels:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {cohort.preferredChannels.map((channel, j) => (
                            <Badge key={j} variant="secondary" className="text-xs">{channel}</Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Real-time Metrics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Real-time System Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {journeyAnalytics.realTimeMetrics.systemPerformance.map((metric, i) => (
                  <div key={i} className="text-center">
                    <div className={`w-16 h-16 mx-auto mb-2 rounded-full flex items-center justify-center ${
                      metric.status === 'Excellent' ? 'bg-green-100' :
                      metric.status === 'Good' ? 'bg-blue-100' :
                      metric.status === 'Warning' ? 'bg-yellow-100' : 'bg-red-100'
                    }`}>
                      <Gauge className={`h-8 w-8 ${
                        metric.status === 'Excellent' ? 'text-green-600' :
                        metric.status === 'Good' ? 'text-blue-600' :
                        metric.status === 'Warning' ? 'text-yellow-600' : 'text-red-600'
                      }`} />
                    </div>
                    <p className="font-medium">{metric.metric}</p>
                    <p className="text-sm text-gray-600">{metric.current} / {metric.target}</p>
                    <Badge 
                      variant="secondary" 
                      className={`text-xs mt-1 ${
                        metric.status === 'Excellent' ? 'bg-green-100 text-green-800' :
                        metric.status === 'Good' ? 'bg-blue-100 text-blue-800' :
                        metric.status === 'Warning' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {metric.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="optimizations" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredOptimizations.map((optimization) => (
              <motion.div
                key={optimization.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="group"
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        {getOptimizationIcon(optimization.category)}
                        <Badge variant="outline" className="capitalize">
                          {optimization.category.replace('_', ' ')}
                        </Badge>
                      </div>
                      <Badge className={getComplexityColor(optimization.implementationComplexity)}>
                        {optimization.implementationComplexity.replace('_', ' ')}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg leading-snug">{optimization.title}</CardTitle>
                    <p className="text-sm text-gray-600 leading-relaxed">{optimization.description}</p>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Priority Scores */}
                    <div className="grid grid-cols-3 gap-2 text-sm">
                      <div className="text-center">
                        <p className="font-bold text-blue-600">{optimization.priorityScore.toFixed(0)}</p>
                        <p className="text-xs text-gray-600">Priority</p>
                      </div>
                      <div className="text-center">
                        <p className="font-bold text-green-600">{optimization.feasibilityScore.toFixed(0)}</p>
                        <p className="text-xs text-gray-600">Feasibility</p>
                      </div>
                      <div className="text-center">
                        <p className="font-bold text-purple-600">{optimization.impactScore.toFixed(0)}</p>
                        <p className="text-xs text-gray-600">Impact</p>
                      </div>
                    </div>

                    {/* Current State Metrics */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Current Performance</h4>
                      <div className="space-y-1">
                        {optimization.currentState.metrics.slice(0, 2).map((metric, i) => (
                          <div key={i} className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">{metric.metric}:</span>
                            <div className="text-right">
                              <span className="font-medium">{metric.current.toFixed(1)}%</span>
                              <span className="text-xs text-gray-500 ml-1">vs {metric.benchmark.toFixed(1)}%</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Expected Outcomes */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Expected Improvements</h4>
                      <div className="space-y-1">
                        {optimization.proposedSolution.expectedOutcomes.metrics.slice(0, 2).map((metric, i) => (
                          <div key={i} className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">{metric.metric}:</span>
                            <span className="font-medium text-green-600">+{metric.improvement.toFixed(1)}%</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* ROI Projection */}
                    <div className="bg-green-50 p-3 rounded-lg text-center">
                      <p className="text-lg font-bold text-green-600">{optimization.roiProjection.toFixed(0)}%</p>
                      <p className="text-xs text-gray-600">Projected ROI</p>
                    </div>

                    {/* Implementation Timeline */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Implementation Phases</h4>
                      <div className="space-y-1">
                        {optimization.proposedSolution.implementation.phases.slice(0, 2).map((phase, i) => (
                          <div key={i} className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">{phase.phase}:</span>
                            <span className="font-medium">{phase.duration}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Eye className="h-4 w-4 mr-2" />
                        Details
                      </Button>
                      <Button size="sm" className="flex-1">
                        <Rocket className="h-4 w-4 mr-2" />
                        Implement
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CrossRealityJourneyMapping;