/**
 * Immersive VR Analytics Dashboard
 * Next-generation virtual reality analytics interface for metaverse commerce
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Eye,
  Glasses,
  Box,
  Zap,
  Activity,
  Users,
  TrendingUp,
  TrendingDown,
  Play,
  Pause,
  RotateCw,
  Navigation,
  Compass,
  Target,
  MousePointer,
  Hand,
  Headphones,
  Monitor,
  Smartphone,
  Gamepad2,
  Settings,
  RefreshCw,
  BarChart3,
  LineChart,
  PieChart,
  Gauge,
  Brain,
  Cpu,
  Wifi,
  Signal,
  Battery,
  Clock,
  Calendar,
  MapPin,
  Globe,
  Building2,
  Home,
  ShoppingBag,
  ShoppingCart,
  CreditCard,
  DollarSign,
  Star,
  Heart,
  ThumbsUp,
  MessageCircle,
  Share,
  Download,
  Upload,
  Search,
  Filter,
  Edit,
  Trash,
  Plus,
  Minus,
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  ArrowLeft,
  Move3d,
  RotateCcw,
  Maximize,
  Minimize,
  Volume2,
  VolumeX,
  Mic,
  MicOff,
  Camera,
  CameraOff,
  Layers,
  Grid3x3,
  Workflow,
  Network,
  Link2,
  Unlock,
  Key,
  Shield,
  CheckCircle,
  AlertTriangle,
  Info,
  HelpCircle,
  BookOpen,
  FileText,
  Image,
  Video,
  Music,
  Archive,
  Folder,
  Mail,
  Phone,
  Bell,
  Flag,
  Award,
  Trophy,
  Crown,
  Gem,
  Sparkles,
  Wand2,
  Palette,
  Brush,
  Scissors,
  PaintBucket,
  Ruler,
  Square,
  Circle,
  Triangle,
  Hexagon,
  Rocket,
  Plane,
  Car,
  Bike,
  Train,
  Ship,
  Anchor,
  Map,
  TreePine,
  Mountain,
  Sun,
  Moon,
  Cloud,
  Snowflake,
  Flame,
  Droplets,
  Wind,
  Lightning,
  Rainbow,
  Flower,
  Leaf
} from 'lucide-react';
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart as RechartsBarChart, Bar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ScatterChart, Scatter, PieChart as RechartsPieChart, Pie } from 'recharts';

// TypeScript interfaces for VR analytics
interface VRSession {
  id: string;
  userId: string;
  username: string;
  avatar: string;
  startTime: string;
  endTime: string;
  duration: number;
  vrPlatform: 'meta_quest' | 'vive' | 'pico' | 'apple_vision' | 'magic_leap' | 'hololens';
  headsetModel: string;
  sessionType: 'shopping' | 'browsing' | 'social' | 'gaming' | 'education' | 'work';
  virtualEnvironment: string;
  interactions: {
    productViews: number;
    productInteractions: number;
    gestureCommands: number;
    voiceCommands: number;
    eyeTracking: number;
    handTracking: number;
  };
  spatialData: {
    positionChanges: number;
    roomscaleMovement: number;
    headRotations: number;
    avgViewDistance: number;
    explorationRadius: number;
  };
  performance: {
    frameRate: number;
    latency: number;
    renderQuality: string;
    motionSickness: boolean;
    comfortRating: number;
  };
  commerce: {
    itemsViewed: number;
    virtualTryOns: number;
    purchases: number;
    cartValue: number;
    wishlistAdds: number;
  };
  biometrics: {
    heartRate: number;
    eyeStrain: number;
    focusTime: number;
    engagementLevel: number;
    stressLevel: number;
  };
  createdAt: string;
}

interface VRMetric {
  timestamp: string;
  activeUsers: number;
  sessionDuration: number;
  interactionRate: number;
  conversionRate: number;
  motionSickness: number;
  userSatisfaction: number;
  technicalQuality: number;
  commerceRevenue: number;
}

interface VirtualEnvironment {
  id: string;
  name: string;
  type: 'store' | 'showroom' | 'experience' | 'social' | 'event' | 'exhibition';
  platform: string;
  isActive: boolean;
  visitors: number;
  averageSessionTime: number;
  interactionHotspots: {
    x: number;
    y: number;
    z: number;
    interactions: number;
    type: string;
  }[];
  commerceMetrics: {
    conversionRate: number;
    averageOrderValue: number;
    totalSales: number;
    topProducts: string[];
  };
  technicalMetrics: {
    frameRate: number;
    loadTime: number;
    bandwidth: number;
    errorRate: number;
  };
  userFeedback: {
    rating: number;
    comfort: number;
    usability: number;
    enjoyment: number;
  };
  createdAt: string;
}

interface VRDevice {
  id: string;
  type: string;
  model: string;
  manufacturer: string;
  capabilities: {
    tracking: '3dof' | '6dof';
    handTracking: boolean;
    eyeTracking: boolean;
    hapticFeedback: boolean;
    spatialAudio: boolean;
    passthrough: boolean;
  };
  performance: {
    resolution: string;
    refreshRate: number;
    fieldOfView: number;
    processingPower: number;
    batteryLife: number;
  };
  usage: {
    activeUsers: number;
    averageSessionTime: number;
    preferenceScore: number;
    technicalIssues: number;
  };
  marketShare: number;
}

interface SpatialAnalytics {
  id: string;
  environmentId: string;
  heatmapData: {
    x: number;
    y: number;
    z: number;
    intensity: number;
    duration: number;
    interactions: number;
  }[];
  pathAnalysis: {
    startPoint: { x: number; y: number; z: number };
    endPoint: { x: number; y: number; z: number };
    pathPoints: { x: number; y: number; z: number; timestamp: string }[];
    efficiency: number;
    commonPath: boolean;
  }[];
  attentionZones: {
    zone: string;
    viewTime: number;
    interactions: number;
    conversionRate: number;
    priority: 'high' | 'medium' | 'low';
  }[];
  behavioralInsights: {
    explorationPattern: 'systematic' | 'random' | 'targeted' | 'social';
    dwellTimeDistribution: Record<string, number>;
    interactionPreferences: Record<string, number>;
    socialBehavior: {
      groupSize: number;
      communicationFrequency: number;
      collaborativeActions: number;
    };
  };
}

interface ImmersiveInsight {
  id: string;
  category: 'user_behavior' | 'technical_performance' | 'commerce_optimization' | 'spatial_design' | 'accessibility';
  insight: string;
  recommendation: string;
  impact: 'low' | 'medium' | 'high' | 'critical';
  confidence: number;
  vrSpecific: boolean;
  implementation: {
    difficulty: 'easy' | 'medium' | 'hard';
    timeframe: string;
    resources: string[];
  };
  expectedOutcome: string;
  createdAt: string;
}

// Mock data generation functions
const generateVRSessions = (): VRSession[] => {
  const platforms = ['meta_quest', 'vive', 'pico', 'apple_vision', 'magic_leap', 'hololens'] as const;
  const sessionTypes = ['shopping', 'browsing', 'social', 'gaming', 'education', 'work'] as const;
  const environments = [
    'Virtual Mall Central', 'Fashion Runway VR', 'Tech Showcase Space', 'Art Gallery Metaverse',
    'Product Demo Theater', 'Social Shopping Plaza', 'Home Design Studio', 'Gaming Merchandise Store'
  ];

  return Array.from({ length: 32 }, (_, i) => ({
    id: `vr-session-${i + 1}`,
    userId: `user-${Math.floor(Math.random() * 1000) + 1}`,
    username: `VRUser${i + 1}`,
    avatar: `avatar-${(i % 10) + 1}`,
    startTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() - Math.random() * 6 * 24 * 60 * 60 * 1000).toISOString(),
    duration: Math.floor(Math.random() * 120) + 5, // 5-125 minutes
    vrPlatform: platforms[i % platforms.length],
    headsetModel: [
      'Quest 3', 'Quest Pro', 'VIVE Pro 2', 'VIVE XR Elite', 'Pico 4',
      'Vision Pro', 'Magic Leap 2', 'HoloLens 2'
    ][i % 8],
    sessionType: sessionTypes[i % sessionTypes.length],
    virtualEnvironment: environments[i % environments.length],
    interactions: {
      productViews: Math.floor(Math.random() * 50) + 5,
      productInteractions: Math.floor(Math.random() * 25) + 2,
      gestureCommands: Math.floor(Math.random() * 100) + 10,
      voiceCommands: Math.floor(Math.random() * 30) + 5,
      eyeTracking: Math.floor(Math.random() * 200) + 50,
      handTracking: Math.floor(Math.random() * 150) + 20
    },
    spatialData: {
      positionChanges: Math.floor(Math.random() * 500) + 50,
      roomscaleMovement: Math.random() * 100 + 10,
      headRotations: Math.floor(Math.random() * 1000) + 100,
      avgViewDistance: Math.random() * 3 + 0.5,
      explorationRadius: Math.random() * 10 + 2
    },
    performance: {
      frameRate: Math.floor(Math.random() * 30) + 60, // 60-90 FPS
      latency: Math.random() * 15 + 5, // 5-20ms
      renderQuality: ['Low', 'Medium', 'High', 'Ultra'][Math.floor(Math.random() * 4)],
      motionSickness: Math.random() > 0.8,
      comfortRating: Math.random() * 3 + 7 // 7-10
    },
    commerce: {
      itemsViewed: Math.floor(Math.random() * 20) + 1,
      virtualTryOns: Math.floor(Math.random() * 10),
      purchases: Math.floor(Math.random() * 5),
      cartValue: Math.random() * 500 + 50,
      wishlistAdds: Math.floor(Math.random() * 8)
    },
    biometrics: {
      heartRate: Math.floor(Math.random() * 40) + 60, // 60-100 BPM
      eyeStrain: Math.random() * 5 + 1, // 1-6 scale
      focusTime: Math.random() * 80 + 20, // 20-100%
      engagementLevel: Math.random() * 40 + 60, // 60-100%
      stressLevel: Math.random() * 30 + 10 // 10-40%
    },
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateVRMetrics = (): VRMetric[] => {
  return Array.from({ length: 24 }, (_, i) => ({
    timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString().split('T')[1].split(':')[0] + ':00',
    activeUsers: Math.floor(Math.random() * 200) + 50,
    sessionDuration: Math.random() * 60 + 20,
    interactionRate: Math.random() * 30 + 70,
    conversionRate: Math.random() * 15 + 5,
    motionSickness: Math.random() * 10 + 2,
    userSatisfaction: Math.random() * 20 + 75,
    technicalQuality: Math.random() * 25 + 70,
    commerceRevenue: Math.random() * 5000 + 1000
  }));
};

const generateVirtualEnvironments = (): VirtualEnvironment[] => {
  const types = ['store', 'showroom', 'experience', 'social', 'event', 'exhibition'] as const;
  const platforms = ['Meta Horizon', 'VRChat', 'Spatial.io', 'Mozilla Hubs', 'AltspaceVR', 'Custom Unity'];

  return Array.from({ length: 12 }, (_, i) => ({
    id: `env-${i + 1}`,
    name: [
      'Virtual Flagship Store',
      'Fashion Week Runway',
      'Interactive Product Showcase',
      'Social Shopping Plaza',
      'Immersive Brand Experience',
      'Digital Art Gallery',
      'Virtual Conference Center',
      'Gaming Merchandise Hub',
      'Home Design Studio',
      'Tech Innovation Lab',
      'Luxury Watch Boutique',
      'Sustainable Fashion Space'
    ][i],
    type: types[i % types.length],
    platform: platforms[i % platforms.length],
    isActive: Math.random() > 0.2,
    visitors: Math.floor(Math.random() * 1000) + 100,
    averageSessionTime: Math.random() * 45 + 15,
    interactionHotspots: Array.from({ length: Math.floor(Math.random() * 10) + 5 }, (_, j) => ({
      x: Math.random() * 20 - 10,
      y: Math.random() * 5,
      z: Math.random() * 20 - 10,
      interactions: Math.floor(Math.random() * 100) + 10,
      type: ['product', 'info', 'social', 'navigation', 'purchase'][j % 5]
    })),
    commerceMetrics: {
      conversionRate: Math.random() * 20 + 5,
      averageOrderValue: Math.random() * 300 + 100,
      totalSales: Math.random() * 50000 + 10000,
      topProducts: [`Product ${i + 1}A`, `Product ${i + 1}B`, `Product ${i + 1}C`]
    },
    technicalMetrics: {
      frameRate: Math.floor(Math.random() * 30) + 60,
      loadTime: Math.random() * 5 + 2,
      bandwidth: Math.random() * 50 + 10,
      errorRate: Math.random() * 2
    },
    userFeedback: {
      rating: Math.random() * 2 + 8,
      comfort: Math.random() * 3 + 7,
      usability: Math.random() * 2.5 + 7.5,
      enjoyment: Math.random() * 2 + 8
    },
    createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateVRDevices = (): VRDevice[] => {
  return [
    {
      id: 'meta-quest-3',
      type: 'Standalone VR',
      model: 'Quest 3',
      manufacturer: 'Meta',
      capabilities: {
        tracking: '6dof',
        handTracking: true,
        eyeTracking: false,
        hapticFeedback: true,
        spatialAudio: true,
        passthrough: true
      },
      performance: {
        resolution: '2064x2208 per eye',
        refreshRate: 120,
        fieldOfView: 110,
        processingPower: 85,
        batteryLife: 180
      },
      usage: {
        activeUsers: 450,
        averageSessionTime: 45,
        preferenceScore: 92,
        technicalIssues: 3
      },
      marketShare: 35.2
    },
    {
      id: 'apple-vision-pro',
      type: 'Mixed Reality',
      model: 'Vision Pro',
      manufacturer: 'Apple',
      capabilities: {
        tracking: '6dof',
        handTracking: true,
        eyeTracking: true,
        hapticFeedback: false,
        spatialAudio: true,
        passthrough: true
      },
      performance: {
        resolution: '3660x3200 per eye',
        refreshRate: 96,
        fieldOfView: 100,
        processingPower: 95,
        batteryLife: 120
      },
      usage: {
        activeUsers: 180,
        averageSessionTime: 35,
        preferenceScore: 88,
        technicalIssues: 5
      },
      marketShare: 15.8
    },
    {
      id: 'htc-vive-pro-2',
      type: 'PC VR',
      model: 'VIVE Pro 2',
      manufacturer: 'HTC',
      capabilities: {
        tracking: '6dof',
        handTracking: false,
        eyeTracking: true,
        hapticFeedback: true,
        spatialAudio: true,
        passthrough: false
      },
      performance: {
        resolution: '2448x2448 per eye',
        refreshRate: 120,
        fieldOfView: 120,
        processingPower: 90,
        batteryLife: 0
      },
      usage: {
        activeUsers: 120,
        averageSessionTime: 65,
        preferenceScore: 85,
        technicalIssues: 7
      },
      marketShare: 12.3
    },
    {
      id: 'pico-4',
      type: 'Standalone VR',
      model: 'Pico 4',
      manufacturer: 'ByteDance',
      capabilities: {
        tracking: '6dof',
        handTracking: true,
        eyeTracking: false,
        hapticFeedback: true,
        spatialAudio: true,
        passthrough: true
      },
      performance: {
        resolution: '2160x2160 per eye',
        refreshRate: 90,
        fieldOfView: 105,
        processingPower: 80,
        batteryLife: 150
      },
      usage: {
        activeUsers: 95,
        averageSessionTime: 40,
        preferenceScore: 78,
        technicalIssues: 4
      },
      marketShare: 8.7
    }
  ];
};

const generateSpatialAnalytics = (): SpatialAnalytics[] => {
  return Array.from({ length: 8 }, (_, i) => ({
    id: `spatial-${i + 1}`,
    environmentId: `env-${i + 1}`,
    heatmapData: Array.from({ length: 50 }, (_, j) => ({
      x: Math.random() * 20 - 10,
      y: Math.random() * 5,
      z: Math.random() * 20 - 10,
      intensity: Math.random() * 100,
      duration: Math.random() * 300 + 30,
      interactions: Math.floor(Math.random() * 20)
    })),
    pathAnalysis: Array.from({ length: 10 }, (_, k) => ({
      startPoint: { x: Math.random() * 20 - 10, y: 0, z: Math.random() * 20 - 10 },
      endPoint: { x: Math.random() * 20 - 10, y: 0, z: Math.random() * 20 - 10 },
      pathPoints: Array.from({ length: 15 }, (_, l) => ({
        x: Math.random() * 20 - 10,
        y: 0,
        z: Math.random() * 20 - 10,
        timestamp: new Date(Date.now() - Math.random() * 60 * 60 * 1000).toISOString()
      })),
      efficiency: Math.random() * 40 + 60,
      commonPath: Math.random() > 0.6
    })),
    attentionZones: [
      { zone: 'Product Display A', viewTime: 45, interactions: 23, conversionRate: 12.5, priority: 'high' as const },
      { zone: 'Interactive Demo', viewTime: 67, interactions: 45, conversionRate: 18.2, priority: 'high' as const },
      { zone: 'Information Kiosk', viewTime: 23, interactions: 12, conversionRate: 5.8, priority: 'medium' as const },
      { zone: 'Social Area', viewTime: 34, interactions: 8, conversionRate: 2.1, priority: 'low' as const },
      { zone: 'Checkout Counter', viewTime: 15, interactions: 35, conversionRate: 89.3, priority: 'high' as const }
    ],
    behavioralInsights: {
      explorationPattern: ['systematic', 'random', 'targeted', 'social'][Math.floor(Math.random() * 4)] as any,
      dwellTimeDistribution: {
        'entrance': 15,
        'main_area': 45,
        'product_zones': 30,
        'social_areas': 10
      },
      interactionPreferences: {
        'hand_gestures': 65,
        'voice_commands': 25,
        'eye_tracking': 10
      },
      socialBehavior: {
        groupSize: Math.random() * 3 + 1,
        communicationFrequency: Math.random() * 50 + 10,
        collaborativeActions: Math.floor(Math.random() * 15) + 2
      }
    }
  }));
};

const generateImmersiveInsights = (): ImmersiveInsight[] => {
  const categories = ['user_behavior', 'technical_performance', 'commerce_optimization', 'spatial_design', 'accessibility'] as const;
  const impacts = ['low', 'medium', 'high', 'critical'] as const;
  const difficulties = ['easy', 'medium', 'hard'] as const;

  return Array.from({ length: 16 }, (_, i) => ({
    id: `insight-${i + 1}`,
    category: categories[i % categories.length],
    insight: [
      'Users spend 40% more time in environments with spatial audio',
      'Hand tracking increases product interaction by 230%',
      'Eye tracking reveals 15-second attention span for product details',
      'Motion sickness correlates with rapid scene transitions',
      'Social presence doubles engagement in virtual stores',
      'Haptic feedback improves purchase confidence by 45%',
      'Voice commands reduce navigation time by 60%',
      'Passthrough mode increases comfort for new VR users',
      '3D product visualization boosts conversion by 85%',
      'Spatial anchors improve object placement accuracy',
      'Gesture shortcuts reduce task completion time',
      'Biometric monitoring prevents user fatigue',
      'Multi-user sessions increase average order value',
      'Realistic lighting enhances product perception',
      'Adaptive UI scaling improves accessibility',
      'Spatial computing enables intuitive interactions'
    ][i],
    recommendation: [
      'Implement 3D spatial audio throughout all environments',
      'Prioritize hand tracking over controller-based interactions',
      'Optimize product detail displays for 15-second viewing',
      'Implement smooth transition animations and comfort settings',
      'Add multiplayer features to shopping experiences',
      'Integrate haptic feedback for all interactive elements',
      'Expand voice command vocabulary and accuracy',
      'Offer passthrough modes for accessibility and comfort',
      'Convert all 2D product images to 3D models',
      'Implement persistent spatial anchoring system',
      'Create gesture-based shortcuts for common actions',
      'Add real-time biometric monitoring and alerts',
      'Enable group shopping and collaborative features',
      'Upgrade to physically-based rendering systems',
      'Implement dynamic UI scaling based on user preferences',
      'Leverage spatial computing APIs for natural interactions'
    ][i],
    impact: impacts[i % impacts.length],
    confidence: Math.random() * 30 + 70,
    vrSpecific: Math.random() > 0.3,
    implementation: {
      difficulty: difficulties[i % difficulties.length],
      timeframe: ['1-2 weeks', '2-4 weeks', '1-2 months', '2-3 months', '3-6 months'][i % 5],
      resources: [
        ['VR developers', 'Audio engineers'],
        ['Hand tracking specialists', 'UI/UX designers'],
        ['Data scientists', 'Frontend developers'],
        ['VR comfort experts', 'Animation team'],
        ['Social platform developers', 'Backend engineers'],
        ['Haptic hardware team', 'Integration specialists'],
        ['Voice recognition team', 'ML engineers'],
        ['AR/VR developers', 'Accessibility team'],
        ['3D modeling team', 'Graphics programmers'],
        ['Spatial computing team', 'Cloud infrastructure'],
        ['Gesture recognition team', 'UX researchers'],
        ['Biometric engineers', 'Health monitoring team'],
        ['Multiplayer specialists', 'Social features team'],
        ['Graphics engineers', 'Lighting artists'],
        ['Accessibility team', 'UI adaptation specialists'],
        ['Spatial computing team', 'API integration specialists']
      ][i]
    },
    expectedOutcome: [
      '40% increase in session duration and user satisfaction',
      '230% improvement in product interaction rates',
      '60% better information retention and recall',
      '75% reduction in motion sickness complaints',
      '100% increase in social engagement metrics',
      '45% improvement in purchase confidence scores',
      '60% faster task completion and navigation',
      '50% better accessibility and user onboarding',
      '85% increase in conversion rates and sales',
      '90% improvement in spatial interaction accuracy',
      '70% faster user workflows and efficiency',
      '80% reduction in user fatigue and session abandonment',
      '120% increase in average order value',
      '65% better product visualization and perception',
      '55% improvement in accessibility compliance',
      '150% more natural and intuitive user interactions'
    ][i],
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const ImmersiveVRDashboard: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [deviceFilter, setDeviceFilter] = useState<string>('all');
  const [environmentFilter, setEnvironmentFilter] = useState<string>('all');
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedSession, setSelectedSession] = useState<string | null>(null);

  // Generate mock data
  const vrSessions = useMemo(() => generateVRSessions(), []);
  const vrMetrics = useMemo(() => generateVRMetrics(), []);
  const virtualEnvironments = useMemo(() => generateVirtualEnvironments(), []);
  const vrDevices = useMemo(() => generateVRDevices(), []);
  const spatialAnalytics = useMemo(() => generateSpatialAnalytics(), []);
  const immersiveInsights = useMemo(() => generateImmersiveInsights(), []);

  // Filter and search logic
  const filteredSessions = useMemo(() => {
    return vrSessions.filter(session => {
      const matchesSearch = session.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          session.virtualEnvironment.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesDevice = deviceFilter === 'all' || session.vrPlatform === deviceFilter;
      const matchesEnvironment = environmentFilter === 'all' || session.virtualEnvironment === environmentFilter;
      return matchesSearch && matchesDevice && matchesEnvironment;
    });
  }, [vrSessions, searchTerm, deviceFilter, environmentFilter]);

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'meta_quest': return <Glasses className="h-4 w-4" />;
      case 'apple_vision': return <Eye className="h-4 w-4" />;
      case 'vive': return <Headphones className="h-4 w-4" />;
      case 'pico': return <Box className="h-4 w-4" />;
      case 'magic_leap': return <Wand2 className="h-4 w-4" />;
      case 'hololens': return <Monitor className="h-4 w-4" />;
      default: return <Glasses className="h-4 w-4" />;
    }
  };

  const getSessionTypeColor = (type: string) => {
    switch (type) {
      case 'shopping': return 'bg-green-500';
      case 'browsing': return 'bg-blue-500';
      case 'social': return 'bg-purple-500';
      case 'gaming': return 'bg-red-500';
      case 'education': return 'bg-yellow-500';
      case 'work': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const getPerformanceColor = (value: number, type: 'fps' | 'latency' | 'comfort') => {
    if (type === 'fps') {
      return value >= 80 ? 'text-green-600' : value >= 60 ? 'text-yellow-600' : 'text-red-600';
    } else if (type === 'latency') {
      return value <= 10 ? 'text-green-600' : value <= 15 ? 'text-yellow-600' : 'text-red-600';
    } else { // comfort
      return value >= 8 ? 'text-green-600' : value >= 6 ? 'text-yellow-600' : 'text-red-600';
    }
  };

  return (
    <div className="p-6 space-y-6 bg-gradient-to-br from-purple-50 via-blue-50 to-cyan-50 min-h-screen">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
            Immersive VR Analytics Dashboard
          </h1>
          <p className="text-gray-600 mt-2">Next-generation virtual reality analytics interface for metaverse commerce</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Refresh VR Data
          </Button>
          <Button className="flex items-center gap-2 bg-gradient-to-r from-purple-500 to-blue-500">
            <Glasses className="h-4 w-4" />
            Enter VR Mode
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sessions">VR Sessions</TabsTrigger>
          <TabsTrigger value="environments">Virtual Environments</TabsTrigger>
          <TabsTrigger value="devices">VR Devices</TabsTrigger>
          <TabsTrigger value="spatial">Spatial Analytics</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm font-medium">Active VR Users</p>
                    <p className="text-3xl font-bold">
                      {vrSessions.filter(s => new Date(s.endTime) > new Date(Date.now() - 60 * 60 * 1000)).length}
                    </p>
                  </div>
                  <Users className="h-8 w-8 text-purple-200" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="bg-purple-400 text-purple-900">
                    +{Math.floor(Math.random() * 30 + 10)}% vs last hour
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm font-medium">Avg Session Time</p>
                    <p className="text-3xl font-bold">
                      {Math.floor(vrSessions.reduce((sum, s) => sum + s.duration, 0) / vrSessions.length)}m
                    </p>
                  </div>
                  <Clock className="h-8 w-8 text-blue-200" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="bg-blue-400 text-blue-900">
                    VR Optimized
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm font-medium">VR Conversion Rate</p>
                    <p className="text-3xl font-bold">
                      {Math.floor(vrSessions.filter(s => s.commerce.purchases > 0).length / vrSessions.length * 100)}%
                    </p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-green-200" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="bg-green-400 text-green-900">
                    +{Math.floor(Math.random() * 20 + 5)}% vs 2D
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-orange-100 text-sm font-medium">VR Revenue</p>
                    <p className="text-3xl font-bold">
                      ${Math.floor(vrSessions.reduce((sum, s) => sum + s.commerce.cartValue, 0) / 1000)}k
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-orange-200" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="bg-orange-400 text-orange-900">
                    Immersive Commerce
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  VR Engagement Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={vrMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="activeUsers" stackId="1" stroke="#8b5cf6" fill="#8b5cf6" fillOpacity={0.6} />
                    <Area type="monotone" dataKey="sessionDuration" stackId="2" stroke="#06b6d4" fill="#06b6d4" fillOpacity={0.6} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gauge className="h-5 w-5" />
                  VR Performance Quality
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsLineChart data={vrMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="technicalQuality" stroke="#10b981" strokeWidth={2} />
                    <Line type="monotone" dataKey="userSatisfaction" stroke="#f59e0b" strokeWidth={2} />
                    <Line type="monotone" dataKey="interactionRate" stroke="#8b5cf6" strokeWidth={2} />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Box className="h-5 w-5" />
                  Virtual Environment Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {virtualEnvironments.filter(env => env.isActive).slice(0, 6).map((env) => (
                    <div key={env.id} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-blue-400 rounded-lg flex items-center justify-center">
                          <Box className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <p className="font-medium">{env.name}</p>
                          <p className="text-sm text-gray-600">{env.platform} • {env.visitors} visitors</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-semibold text-green-600">
                          {env.commerceMetrics.conversionRate.toFixed(1)}%
                        </p>
                        <p className="text-sm text-gray-600">Conversion</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Headphones className="h-5 w-5" />
                  VR Device Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {vrDevices.map((device) => (
                    <div key={device.id} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getPlatformIcon(device.id)}
                        <div>
                          <p className="font-medium text-sm">{device.model}</p>
                          <p className="text-xs text-gray-600">{device.manufacturer}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-sm">{device.marketShare.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">{device.usage.activeUsers} users</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="sessions" className="space-y-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search VR sessions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={deviceFilter}
                onChange={(e) => setDeviceFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Devices</option>
                <option value="meta_quest">Meta Quest</option>
                <option value="apple_vision">Apple Vision</option>
                <option value="vive">HTC VIVE</option>
                <option value="pico">Pico</option>
              </select>
              <select
                value={environmentFilter}
                onChange={(e) => setEnvironmentFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Environments</option>
                {Array.from(new Set(vrSessions.map(s => s.virtualEnvironment))).map(env => (
                  <option key={env} value={env}>{env}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="grid gap-4">
            <AnimatePresence>
              {filteredSessions.map((session) => (
                <motion.div
                  key={session.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card className="hover:shadow-lg transition-shadow bg-gradient-to-r from-white to-gray-50">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-blue-400 rounded-full flex items-center justify-center text-white font-bold">
                              {session.username.slice(0, 2).toUpperCase()}
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold">{session.username}</h3>
                              <p className="text-sm text-gray-600">{session.virtualEnvironment}</p>
                            </div>
                            <div className={`w-3 h-3 rounded-full ${getSessionTypeColor(session.sessionType)}`} />
                            <Badge variant="outline" className="flex items-center gap-1">
                              {getPlatformIcon(session.vrPlatform)}
                              {session.headsetModel}
                            </Badge>
                            <Badge variant="secondary">{session.duration}min</Badge>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 text-sm">
                            <div>
                              <p className="text-gray-600">Interactions</p>
                              <p className="font-semibold">{session.interactions.productInteractions}</p>
                              <p className="text-xs text-gray-500">
                                {session.interactions.gestureCommands} gestures, {session.interactions.voiceCommands} voice
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-600">Commerce</p>
                              <p className="font-semibold text-green-600">${Math.floor(session.commerce.cartValue)}</p>
                              <p className="text-xs text-gray-500">
                                {session.commerce.purchases} purchases, {session.commerce.virtualTryOns} try-ons
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-600">Performance</p>
                              <p className={`font-semibold ${getPerformanceColor(session.performance.frameRate, 'fps')}`}>
                                {Math.floor(session.performance.frameRate)} FPS
                              </p>
                              <p className={`text-xs ${getPerformanceColor(session.performance.latency, 'latency')}`}>
                                {session.performance.latency.toFixed(1)}ms latency
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-600">Comfort</p>
                              <p className={`font-semibold ${getPerformanceColor(session.performance.comfortRating, 'comfort')}`}>
                                {session.performance.comfortRating.toFixed(1)}/10
                              </p>
                              <p className="text-xs text-gray-500">
                                {session.performance.motionSickness ? 'Motion sick' : 'Comfortable'}
                              </p>
                            </div>
                          </div>

                          <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-4 rounded-lg">
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                              <div>
                                <p className="text-gray-600">Heart Rate</p>
                                <p className="font-medium">{Math.floor(session.biometrics.heartRate)} BPM</p>
                              </div>
                              <div>
                                <p className="text-gray-600">Engagement</p>
                                <p className="font-medium">{Math.floor(session.biometrics.engagementLevel)}%</p>
                              </div>
                              <div>
                                <p className="text-gray-600">Focus Time</p>
                                <p className="font-medium">{Math.floor(session.biometrics.focusTime)}%</p>
                              </div>
                              <div>
                                <p className="text-gray-600">Stress Level</p>
                                <p className="font-medium">{Math.floor(session.biometrics.stressLevel)}%</p>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col gap-2">
                          <Button size="sm" variant="outline" className="flex items-center gap-1">
                            <Eye className="h-4 w-4" />
                            3D Replay
                          </Button>
                          <Button size="sm" variant="outline" className="flex items-center gap-1">
                            <BarChart3 className="h-4 w-4" />
                            Analytics
                          </Button>
                          <Button size="sm" variant="outline">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </TabsContent>

        <TabsContent value="environments" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Virtual Environments</h2>
            <Button className="flex items-center gap-2 bg-gradient-to-r from-purple-500 to-blue-500">
              <Plus className="h-4 w-4" />
              Create Environment
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {virtualEnvironments.map((env) => (
              <Card key={env.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{env.name}</CardTitle>
                    <Badge variant={env.isActive ? "default" : "secondary"}>
                      {env.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="capitalize">{env.type}</Badge>
                    <Badge variant="outline">{env.platform}</Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Visitors</p>
                      <p className="font-semibold">{env.visitors.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Avg Session</p>
                      <p className="font-semibold">{Math.floor(env.averageSessionTime)}min</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Conversion</p>
                      <p className="font-semibold text-green-600">{env.commerceMetrics.conversionRate.toFixed(1)}%</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Revenue</p>
                      <p className="font-semibold">${Math.floor(env.commerceMetrics.totalSales / 1000)}k</p>
                    </div>
                  </div>
                  
                  <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-3 rounded-lg">
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div>
                        <p className="text-gray-600">Frame Rate</p>
                        <p className={`font-medium ${getPerformanceColor(env.technicalMetrics.frameRate, 'fps')}`}>
                          {Math.floor(env.technicalMetrics.frameRate)} FPS
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-600">Load Time</p>
                        <p className="font-medium">{env.technicalMetrics.loadTime.toFixed(1)}s</p>
                      </div>
                      <div>
                        <p className="text-gray-600">User Rating</p>
                        <p className="font-medium text-yellow-600">
                          {env.userFeedback.rating.toFixed(1)} ⭐
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-600">Comfort</p>
                        <p className="font-medium">{env.userFeedback.comfort.toFixed(1)}/10</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">Top Products:</p>
                    <div className="flex flex-wrap gap-1">
                      {env.commerceMetrics.topProducts.map((product, idx) => (
                        <Badge key={idx} variant="secondary" className="text-xs">
                          {product}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1 flex items-center gap-1">
                      <Glasses className="h-4 w-4" />
                      Enter VR
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1 flex items-center gap-1">
                      <BarChart3 className="h-4 w-4" />
                      Analytics
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="devices" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">VR Device Analytics</h2>
            <Button variant="outline" className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Update Device Data
            </Button>
          </div>

          <div className="grid gap-6">
            {vrDevices.map((device) => (
              <Card key={device.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-blue-400 rounded-lg flex items-center justify-center">
                          {getPlatformIcon(device.id)}
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold">{device.model}</h3>
                          <p className="text-gray-600">{device.manufacturer} • {device.type}</p>
                        </div>
                        <Badge variant="outline" className="ml-auto">
                          {device.marketShare.toFixed(1)}% Market Share
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <div>
                          <h4 className="font-semibold text-gray-700 mb-3">Performance</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span>Resolution:</span>
                              <span className="font-medium">{device.performance.resolution}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Refresh Rate:</span>
                              <span className="font-medium">{device.performance.refreshRate}Hz</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Field of View:</span>
                              <span className="font-medium">{device.performance.fieldOfView}°</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Processing Power:</span>
                              <span className="font-medium">{device.performance.processingPower}/100</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Battery Life:</span>
                              <span className="font-medium">
                                {device.performance.batteryLife ? `${device.performance.batteryLife}min` : 'Wired'}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-semibold text-gray-700 mb-3">Capabilities</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex items-center gap-2">
                              <div className={`w-2 h-2 rounded-full ${device.capabilities.tracking === '6dof' ? 'bg-green-500' : 'bg-yellow-500'}`} />
                              <span>{device.capabilities.tracking.toUpperCase()} Tracking</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className={`w-2 h-2 rounded-full ${device.capabilities.handTracking ? 'bg-green-500' : 'bg-gray-400'}`} />
                              <span>Hand Tracking</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className={`w-2 h-2 rounded-full ${device.capabilities.eyeTracking ? 'bg-green-500' : 'bg-gray-400'}`} />
                              <span>Eye Tracking</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className={`w-2 h-2 rounded-full ${device.capabilities.hapticFeedback ? 'bg-green-500' : 'bg-gray-400'}`} />
                              <span>Haptic Feedback</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className={`w-2 h-2 rounded-full ${device.capabilities.spatialAudio ? 'bg-green-500' : 'bg-gray-400'}`} />
                              <span>Spatial Audio</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className={`w-2 h-2 rounded-full ${device.capabilities.passthrough ? 'bg-green-500' : 'bg-gray-400'}`} />
                              <span>Passthrough</span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-semibold text-gray-700 mb-3">Usage Analytics</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span>Active Users:</span>
                              <span className="font-medium">{device.usage.activeUsers.toLocaleString()}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Avg Session:</span>
                              <span className="font-medium">{device.usage.averageSessionTime}min</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Preference Score:</span>
                              <span className="font-medium text-green-600">{device.usage.preferenceScore}/100</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Technical Issues:</span>
                              <span className={`font-medium ${device.usage.technicalIssues > 5 ? 'text-red-600' : 'text-green-600'}`}>
                                {device.usage.technicalIssues}%
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="spatial" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Spatial Analytics</h2>
            <div className="flex gap-2">
              <Button variant="outline" className="flex items-center gap-2">
                <Move3d className="h-4 w-4" />
                3D Heatmap
              </Button>
              <Button className="flex items-center gap-2">
                <Navigation className="h-4 w-4" />
                Path Analysis
              </Button>
            </div>
          </div>

          <div className="grid gap-6">
            {spatialAnalytics.slice(0, 4).map((spatial) => (
              <Card key={spatial.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Compass className="h-5 w-5" />
                    {virtualEnvironments.find(env => env.id === spatial.environmentId)?.name || 'Virtual Environment'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-gray-700 mb-3">Attention Zones</h4>
                      <div className="space-y-3">
                        {spatial.attentionZones.map((zone, idx) => (
                          <div key={idx} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex-1">
                              <p className="font-medium">{zone.zone}</p>
                              <p className="text-sm text-gray-600">
                                {zone.viewTime}s avg view • {zone.interactions} interactions
                              </p>
                            </div>
                            <div className="text-right">
                              <p className={`font-semibold ${
                                zone.priority === 'high' ? 'text-green-600' :
                                zone.priority === 'medium' ? 'text-yellow-600' :
                                'text-gray-600'
                              }`}>
                                {zone.conversionRate.toFixed(1)}%
                              </p>
                              <Badge variant={
                                zone.priority === 'high' ? 'default' :
                                zone.priority === 'medium' ? 'secondary' :
                                'outline'
                              } className="text-xs">
                                {zone.priority}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold text-gray-700 mb-3">Behavioral Insights</h4>
                      <div className="space-y-4">
                        <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-4 rounded-lg">
                          <p className="font-medium text-purple-700 mb-2">Exploration Pattern</p>
                          <p className="text-sm text-gray-700 capitalize">{spatial.behavioralInsights.explorationPattern}</p>
                        </div>
                        
                        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-lg">
                          <p className="font-medium text-blue-700 mb-2">Social Behavior</p>
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div>
                              <p className="text-gray-600">Group Size:</p>
                              <p className="font-medium">{spatial.behavioralInsights.socialBehavior.groupSize.toFixed(1)}</p>
                            </div>
                            <div>
                              <p className="text-gray-600">Collaboration:</p>
                              <p className="font-medium">{spatial.behavioralInsights.socialBehavior.collaborativeActions}</p>
                            </div>
                          </div>
                        </div>

                        <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg">
                          <p className="font-medium text-green-700 mb-2">Interaction Preferences</p>
                          <div className="space-y-1 text-sm">
                            {Object.entries(spatial.behavioralInsights.interactionPreferences).map(([method, percentage]) => (
                              <div key={method} className="flex justify-between">
                                <span className="capitalize">{method.replace('_', ' ')}:</span>
                                <span className="font-medium">{percentage}%</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-700 mb-3">Spatial Heatmap Data</h4>
                    <div className="bg-gradient-to-r from-gray-100 to-gray-200 p-4 rounded-lg">
                      <ResponsiveContainer width="100%" height={200}>
                        <ScatterChart data={spatial.heatmapData.slice(0, 20)}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="x" type="number" name="X Position" />
                          <YAxis dataKey="z" type="number" name="Z Position" />
                          <Tooltip cursor={{ strokeDasharray: '3 3' }} />
                          <Scatter dataKey="intensity" fill="#8b5cf6" />
                        </ScatterChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gauge className="h-5 w-5" />
                  VR Performance Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsLineChart data={vrMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="technicalQuality" stroke="#10b981" strokeWidth={2} />
                    <Line type="monotone" dataKey="userSatisfaction" stroke="#f59e0b" strokeWidth={2} />
                    <Line type="monotone" dataKey="motionSickness" stroke="#ef4444" strokeWidth={2} />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  User Engagement & Conversion
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={vrMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="interactionRate" stroke="#8b5cf6" fill="#8b5cf6" fillOpacity={0.6} />
                    <Area type="monotone" dataKey="conversionRate" stroke="#06b6d4" fill="#06b6d4" fillOpacity={0.6} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                VR Commerce Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <RechartsBarChart data={vrMetrics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="timestamp" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="commerceRevenue" fill="#10b981" />
                </RechartsBarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Immersive Analytics Insights</h2>
            <Button className="flex items-center gap-2 bg-gradient-to-r from-purple-500 to-blue-500">
              <Brain className="h-4 w-4" />
              Generate VR Insights
            </Button>
          </div>

          <div className="grid gap-4">
            {immersiveInsights.map((insight) => (
              <Card key={insight.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <Badge variant="outline" className="capitalize">
                          {insight.category.replace('_', ' ')}
                        </Badge>
                        <Badge variant={
                          insight.impact === 'critical' ? 'destructive' :
                          insight.impact === 'high' ? 'default' :
                          insight.impact === 'medium' ? 'secondary' :
                          'outline'
                        }>
                          {insight.impact} impact
                        </Badge>
                        <Badge variant="outline">{Math.floor(insight.confidence)}% confidence</Badge>
                        {insight.vrSpecific && (
                          <Badge variant="outline" className="flex items-center gap-1 border-purple-500 text-purple-700">
                            <Glasses className="h-3 w-3" />
                            VR Specific
                          </Badge>
                        )}
                      </div>
                      <h3 className="text-lg font-semibold mb-2">{insight.insight}</h3>
                      <p className="text-gray-600 mb-4">{insight.recommendation}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 text-sm">
                        <div>
                          <p className="text-gray-600">Implementation</p>
                          <p className="font-medium capitalize">{insight.implementation.difficulty}</p>
                          <p className="text-xs text-gray-500">{insight.implementation.timeframe}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Resources Needed</p>
                          <p className="font-medium">{insight.implementation.resources.join(', ')}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Expected Outcome</p>
                          <p className="font-medium text-green-600">{insight.expectedOutcome}</p>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button size="sm" className="flex items-center gap-1">
                        <CheckCircle className="h-4 w-4" />
                        Implement
                      </Button>
                      <Button variant="outline" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ImmersiveVRDashboard;