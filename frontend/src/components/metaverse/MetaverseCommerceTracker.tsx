/**
 * Metaverse Commerce Tracking System
 * Advanced analytics for virtual world transactions and digital asset commerce
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Globe,
  ShoppingBag,
  Coins,
  Gem,
  Zap,
  TrendingUp,
  TrendingDown,
  Users,
  Building2,
  Crown,
  Sparkles,
  Wallet,
  CreditCard,
  DollarSign,
  BarChart3,
  LineChart,
  PieChart,
  Activity,
  Eye,
  Settings,
  RefreshCw,
  Search,
  Filter,
  Download,
  Upload,
  Share,
  ExternalLink,
  Plus,
  Minus,
  Edit,
  Trash,
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  Clock,
  Calendar,
  MapPin,
  Star,
  Heart,
  ThumbsUp,
  MessageCircle,
  Flag,
  Award,
  Trophy,
  Target,
  Gauge,
  Shield,
  Lock,
  Unlock,
  Key,
  Link2,
  Network,
  Database,
  Server,
  Cloud,
  Cpu,
  Brain,
  Lightbulb,
  Rocket,
  Plane,
  Car,
  Home,
  Store,
  Factory,
  Warehouse,
  School,
  Hospital,
  Library,
  Theater,
  Camera,
  Video,
  Music,
  Image,
  File,
  FileText,
  Folder,
  Archive,
  Mail,
  Phone,
  Bell,
  Volume2,
  Mic,
  Speaker,
  Headphones,
  Smartphone,
  Laptop,
  Monitor,
  Tv,
  Watch,
  Gamepad2,
  Joystick,
  Keyboard,
  Mouse,
  Printer,
  Scanner,
  Webcam,
  Microphone,
  Router,
  Wifi,
  Signal,
  Battery,
  Power,
  Plug,
  Bluetooth,
  Usb,
  HardDrive,
  SdCard,
  Disc,
  Save,
  FolderOpen,
  FileDown,
  FileUp,
  FilePlus,
  FileMinus,
  FileX,
  FileCheck
} from 'lucide-react';
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart as RechartsBarChart, Bar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ScatterChart, Scatter, ComposedChart, PieChart as RechartsPieChart, Pie } from 'recharts';

// TypeScript interfaces for metaverse commerce
interface MetaverseTransaction {
  id: string;
  userId: string;
  userName: string;
  avatar: string;
  transactionType: 'purchase' | 'sale' | 'trade' | 'auction' | 'gift' | 'rental' | 'subscription';
  metaverse: 'horizon_worlds' | 'vrchat' | 'roblox' | 'fortnite' | 'minecraft' | 'decentraland' | 'sandbox' | 'spatial';
  virtualWorld: string;
  location: string;
  item: {
    id: string;
    name: string;
    category: 'avatar' | 'wearable' | 'accessory' | 'virtual_real_estate' | 'digital_art' | 'utility' | 'experience' | 'currency';
    rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary' | 'mythic';
    isNFT: boolean;
    blockchain?: string;
    tokenId?: string;
  };
  pricing: {
    amount: number;
    currency: 'usd' | 'eth' | 'btc' | 'matic' | 'robux' | 'vbucks' | 'minecoins' | 'mana' | 'sand';
    usdValue: number;
    gasFeesUSD?: number;
  };
  paymentMethod: 'crypto' | 'credit_card' | 'platform_currency' | 'bank_transfer' | 'paypal' | 'apple_pay' | 'google_pay';
  seller?: {
    id: string;
    name: string;
    type: 'individual' | 'brand' | 'creator' | 'marketplace';
    reputation: number;
  };
  marketplace?: string;
  socialContext: {
    wasInfluencerRecommended: boolean;
    sharedInSocial: boolean;
    friendsOwned: number;
    trendingScore: number;
  };
  userJourney: {
    discoverySource: string;
    timeToDecision: number;
    viewsBeforePurchase: number;
    comparisonsViewed: number;
  };
  timestamp: string;
  completedAt: string;
}

interface MetaverseMetric {
  timestamp: string;
  totalTransactions: number;
  totalVolume: number;
  averageOrderValue: number;
  uniqueBuyers: number;
  conversionRate: number;
  nftSales: number;
  virtualRealEstateSales: number;
  avatarSales: number;
  crossPlatformSales: number;
}

interface VirtualMarketplace {
  id: string;
  name: string;
  metaverse: string;
  type: 'official' | 'third_party' | 'decentralized' | 'peer_to_peer';
  categories: string[];
  isActive: boolean;
  metrics: {
    totalListings: number;
    activeBuyers: number;
    activeSellers: number;
    monthlyVolume: number;
    averageOrderValue: number;
    commissionRate: number;
  };
  popularItems: {
    category: string;
    count: number;
    averagePrice: number;
  }[];
  technicalInfo: {
    blockchain?: string;
    smartContract?: string;
    royaltySupport: boolean;
    fractionalOwnership: boolean;
  };
  userRatings: {
    overall: number;
    security: number;
    usability: number;
    support: number;
  };
  createdAt: string;
}

interface DigitalAsset {
  id: string;
  name: string;
  creator: string;
  owner: string;
  category: string;
  rarity: string;
  isNFT: boolean;
  blockchain?: string;
  contractAddress?: string;
  tokenId?: string;
  marketplaces: string[];
  pricing: {
    currentPrice: number;
    currency: string;
    priceHistory: { date: string; price: number; volume: number }[];
    highestSale: number;
    lowestSale: number;
  };
  ownership: {
    totalSupply: number;
    currentOwners: number;
    isUnique: boolean;
    transferable: boolean;
    royaltyPercentage?: number;
  };
  engagement: {
    views: number;
    likes: number;
    shares: number;
    comments: number;
    uses: number;
  };
  utility: {
    inGameBenefits: string[];
    platformsSupported: string[];
    expirationDate?: string;
    upgradeability: boolean;
  };
  createdAt: string;
  lastSaleAt?: string;
}

interface VirtualBrand {
  id: string;
  name: string;
  logo: string;
  category: 'fashion' | 'gaming' | 'art' | 'music' | 'sports' | 'tech' | 'lifestyle';
  metaversePresence: {
    platforms: string[];
    virtualStores: number;
    experiences: number;
    partnerships: string[];
  };
  digitalProducts: {
    totalItems: number;
    nftCollections: number;
    wearables: number;
    accessories: number;
    virtualRealEstate: number;
  };
  commerceMetrics: {
    monthlyRevenue: number;
    totalSales: number;
    averageOrderValue: number;
    conversionRate: number;
    customerRetention: number;
  };
  socialMetrics: {
    followers: number;
    engagement: number;
    brandMentions: number;
    userGeneratedContent: number;
  };
  marketPosition: {
    ranking: number;
    marketShare: number;
    competitorComparison: {
      name: string;
      metric: string;
      value: number;
    }[];
  };
  createdAt: string;
}

interface CrossPlatformAnalytics {
  userId: string;
  userName: string;
  platforms: {
    platform: string;
    timeSpent: number;
    purchases: number;
    totalSpent: number;
    favoriteCategories: string[];
    socialConnections: number;
  }[];
  unifiedMetrics: {
    totalSpent: number;
    totalTimeSpent: number;
    preferredPlatform: string;
    crossPlatformSynergies: {
      platform1: string;
      platform2: string;
      correlationScore: number;
    }[];
  };
  behaviorPatterns: {
    spendingPattern: 'consistent' | 'seasonal' | 'event_driven' | 'impulsive';
    platformLoyalty: number;
    categoryPreferences: Record<string, number>;
    priceRangePreference: {
      min: number;
      max: number;
      average: number;
    };
  };
  recommendations: {
    suggestedPlatforms: string[];
    potentialSpending: number;
    crossSellOpportunities: string[];
  };
}

interface MetaverseInsight {
  id: string;
  category: 'market_trend' | 'user_behavior' | 'platform_performance' | 'brand_opportunity' | 'technology_adoption';
  insight: string;
  recommendation: string;
  impact: 'low' | 'medium' | 'high' | 'critical';
  confidence: number;
  timeframe: string;
  affectedPlatforms: string[];
  relatedMetrics: {
    metric: string;
    change: number;
    period: string;
  }[];
  implementation: {
    steps: string[];
    resources: string[];
    timeline: string;
  };
  expectedOutcome: string;
  createdAt: string;
}

// Mock data generation functions
const generateMetaverseTransactions = (): MetaverseTransaction[] => {
  const transactionTypes = ['purchase', 'sale', 'trade', 'auction', 'gift', 'rental', 'subscription'] as const;
  const metaverses = ['horizon_worlds', 'vrchat', 'roblox', 'fortnite', 'minecraft', 'decentraland', 'sandbox', 'spatial'] as const;
  const categories = ['avatar', 'wearable', 'accessory', 'virtual_real_estate', 'digital_art', 'utility', 'experience', 'currency'] as const;
  const rarities = ['common', 'uncommon', 'rare', 'epic', 'legendary', 'mythic'] as const;
  const currencies = ['usd', 'eth', 'btc', 'matic', 'robux', 'vbucks', 'minecoins', 'mana', 'sand'] as const;
  const paymentMethods = ['crypto', 'credit_card', 'platform_currency', 'bank_transfer', 'paypal', 'apple_pay', 'google_pay'] as const;

  return Array.from({ length: 50 }, (_, i) => ({
    id: `tx-${i + 1}`,
    userId: `user-${Math.floor(Math.random() * 1000) + 1}`,
    userName: `MetaUser${i + 1}`,
    avatar: `avatar-${(i % 20) + 1}`,
    transactionType: transactionTypes[i % transactionTypes.length],
    metaverse: metaverses[i % metaverses.length],
    virtualWorld: [
      'Central Plaza', 'Creator Hub', 'Fashion District', 'Gaming Arena', 'Art Gallery',
      'Music Venue', 'Social Lounge', 'Shopping Mall', 'Event Space', 'Educational Center'
    ][i % 10],
    location: `Sector ${Math.floor(i / 5) + 1}`,
    item: {
      id: `item-${i + 1}`,
      name: [
        'Cyberpunk Jacket', 'Holographic Wings', 'Digital Crown', 'Neon Sneakers', 'Crystal Sword',
        'Virtual Mansion', 'Art Sculpture', 'Music NFT', 'Gaming Skin', 'Avatar Pose',
        'Sparkle Trail', 'Galaxy Backdrop', 'Pixel Pet', 'Rare Emote', 'VIP Pass',
        'Designer Outfit', 'Legendary Mount', 'Premium Experience', 'Crypto Badge', 'Time Capsule'
      ][i % 20],
      category: categories[i % categories.length],
      rarity: rarities[i % rarities.length],
      isNFT: Math.random() > 0.6,
      blockchain: Math.random() > 0.4 ? ['Ethereum', 'Polygon', 'Solana', 'Binance Smart Chain'][Math.floor(Math.random() * 4)] : undefined,
      tokenId: Math.random() > 0.4 ? `${Math.floor(Math.random() * 10000)}` : undefined
    },
    pricing: {
      amount: Math.random() * 500 + 10,
      currency: currencies[i % currencies.length],
      usdValue: Math.random() * 500 + 10,
      gasFeesUSD: Math.random() > 0.5 ? Math.random() * 20 + 2 : undefined
    },
    paymentMethod: paymentMethods[i % paymentMethods.length],
    seller: Math.random() > 0.3 ? {
      id: `seller-${Math.floor(Math.random() * 100) + 1}`,
      name: `Creator${Math.floor(Math.random() * 100) + 1}`,
      type: ['individual', 'brand', 'creator', 'marketplace'][Math.floor(Math.random() * 4)] as any,
      reputation: Math.random() * 30 + 70
    } : undefined,
    marketplace: Math.random() > 0.4 ? ['OpenSea', 'Rarible', 'SuperRare', 'Foundation', 'Official Store'][Math.floor(Math.random() * 5)] : undefined,
    socialContext: {
      wasInfluencerRecommended: Math.random() > 0.7,
      sharedInSocial: Math.random() > 0.6,
      friendsOwned: Math.floor(Math.random() * 10),
      trendingScore: Math.random() * 100
    },
    userJourney: {
      discoverySource: ['social_media', 'friend_recommendation', 'marketplace_browse', 'influencer', 'advertising'][Math.floor(Math.random() * 5)],
      timeToDecision: Math.random() * 72 + 1, // hours
      viewsBeforePurchase: Math.floor(Math.random() * 20) + 1,
      comparisonsViewed: Math.floor(Math.random() * 8)
    },
    timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    completedAt: new Date(Date.now() - Math.random() * 6 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateMetaverseMetrics = (): MetaverseMetric[] => {
  return Array.from({ length: 24 }, (_, i) => ({
    timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString().split('T')[1].split(':')[0] + ':00',
    totalTransactions: Math.floor(Math.random() * 100) + 50,
    totalVolume: Math.random() * 50000 + 10000,
    averageOrderValue: Math.random() * 200 + 50,
    uniqueBuyers: Math.floor(Math.random() * 200) + 100,
    conversionRate: Math.random() * 10 + 5,
    nftSales: Math.floor(Math.random() * 30) + 10,
    virtualRealEstateSales: Math.floor(Math.random() * 10) + 2,
    avatarSales: Math.floor(Math.random() * 40) + 20,
    crossPlatformSales: Math.floor(Math.random() * 15) + 5
  }));
};

const generateVirtualMarketplaces = (): VirtualMarketplace[] => {
  const types = ['official', 'third_party', 'decentralized', 'peer_to_peer'] as const;
  const metaverses = ['Horizon Worlds', 'VRChat', 'Roblox', 'Fortnite', 'Minecraft', 'Decentraland', 'The Sandbox', 'Spatial'];

  return Array.from({ length: 12 }, (_, i) => ({
    id: `marketplace-${i + 1}`,
    name: [
      'MetaMarket Central', 'VirtualBay', 'CryptoCollectibles', 'DigitalAssets Hub',
      'NFT Bazaar', 'Avatar Emporium', 'Virtual Real Estate Exchange', 'Creator Marketplace',
      'Gaming Goods Gallery', 'Decentralized Digital Mall', 'Cross-Platform Store', 'Metaverse Mega Mall'
    ][i],
    metaverse: metaverses[i % metaverses.length],
    type: types[i % types.length],
    categories: [
      ['Avatars', 'Wearables', 'Accessories'],
      ['Virtual Real Estate', 'Digital Art', 'Collectibles'],
      ['Gaming Items', 'Utilities', 'Experiences'],
      ['Fashion', 'Luxury', 'Limited Edition']
    ][i % 4],
    isActive: Math.random() > 0.1,
    metrics: {
      totalListings: Math.floor(Math.random() * 10000) + 1000,
      activeBuyers: Math.floor(Math.random() * 5000) + 500,
      activeSellers: Math.floor(Math.random() * 2000) + 200,
      monthlyVolume: Math.random() * 1000000 + 100000,
      averageOrderValue: Math.random() * 300 + 50,
      commissionRate: Math.random() * 10 + 2.5
    },
    popularItems: [
      { category: 'Avatars', count: Math.floor(Math.random() * 500) + 100, averagePrice: Math.random() * 100 + 20 },
      { category: 'Wearables', count: Math.floor(Math.random() * 1000) + 200, averagePrice: Math.random() * 50 + 10 },
      { category: 'Digital Art', count: Math.floor(Math.random() * 300) + 50, averagePrice: Math.random() * 500 + 100 }
    ],
    technicalInfo: {
      blockchain: Math.random() > 0.5 ? ['Ethereum', 'Polygon', 'Solana'][Math.floor(Math.random() * 3)] : undefined,
      smartContract: Math.random() > 0.5 ? `0x${Math.random().toString(16).substr(2, 8)}...` : undefined,
      royaltySupport: Math.random() > 0.3,
      fractionalOwnership: Math.random() > 0.7
    },
    userRatings: {
      overall: Math.random() * 2 + 8,
      security: Math.random() * 2 + 7.5,
      usability: Math.random() * 2 + 7.8,
      support: Math.random() * 2 + 7.2
    },
    createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateDigitalAssets = (): DigitalAsset[] => {
  const categories = ['Avatar', 'Wearable', 'Accessory', 'Virtual Real Estate', 'Digital Art', 'Utility', 'Experience'];
  const rarities = ['Common', 'Uncommon', 'Rare', 'Epic', 'Legendary', 'Mythic'];
  const currencies = ['ETH', 'MATIC', 'SOL', 'USD'];

  return Array.from({ length: 20 }, (_, i) => ({
    id: `asset-${i + 1}`,
    name: [
      'Cosmic Dragon Wings', 'Holographic Suit', 'Crystal Palace', 'Neon Cityscape',
      'Digital Mona Lisa', 'Time Travel Portal', 'Gravity Boots', 'Ethereal Crown',
      'Pixel Art Garden', 'Quantum Sword', 'Floating Island', 'Aurora Dress',
      'Cyberpunk Mask', 'Golden Throne', 'Infinity Gauntlet', 'Phoenix Feather',
      'Diamond Armor', 'Teleporter Hub', 'Music Box NFT', 'Starship Cockpit'
    ][i],
    creator: `Creator${Math.floor(Math.random() * 50) + 1}`,
    owner: `Owner${Math.floor(Math.random() * 100) + 1}`,
    category: categories[i % categories.length],
    rarity: rarities[i % rarities.length],
    isNFT: Math.random() > 0.4,
    blockchain: Math.random() > 0.3 ? ['Ethereum', 'Polygon', 'Solana'][Math.floor(Math.random() * 3)] : undefined,
    contractAddress: Math.random() > 0.3 ? `0x${Math.random().toString(16).substr(2, 8)}...` : undefined,
    tokenId: Math.random() > 0.3 ? `${Math.floor(Math.random() * 10000)}` : undefined,
    marketplaces: ['OpenSea', 'Rarible', 'Foundation'].slice(0, Math.floor(Math.random() * 3) + 1),
    pricing: {
      currentPrice: Math.random() * 1000 + 50,
      currency: currencies[Math.floor(Math.random() * currencies.length)],
      priceHistory: Array.from({ length: 10 }, (_, j) => ({
        date: new Date(Date.now() - j * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        price: Math.random() * 1000 + 50,
        volume: Math.floor(Math.random() * 100) + 10
      })),
      highestSale: Math.random() * 2000 + 100,
      lowestSale: Math.random() * 100 + 10
    },
    ownership: {
      totalSupply: Math.random() > 0.8 ? Math.floor(Math.random() * 1000) + 1 : 1,
      currentOwners: Math.random() > 0.8 ? Math.floor(Math.random() * 100) + 1 : 1,
      isUnique: Math.random() > 0.6,
      transferable: Math.random() > 0.1,
      royaltyPercentage: Math.random() > 0.5 ? Math.random() * 10 + 2.5 : undefined
    },
    engagement: {
      views: Math.floor(Math.random() * 10000) + 100,
      likes: Math.floor(Math.random() * 1000) + 10,
      shares: Math.floor(Math.random() * 500) + 5,
      comments: Math.floor(Math.random() * 200) + 2,
      uses: Math.floor(Math.random() * 50) + 1
    },
    utility: {
      inGameBenefits: [
        'Enhanced Avatar', 'Special Abilities', 'Exclusive Access', 'Bonus Rewards',
        'Unique Animations', 'VIP Status', 'Gameplay Advantages'
      ].slice(0, Math.floor(Math.random() * 4) + 1),
      platformsSupported: ['Horizon Worlds', 'VRChat', 'Roblox', 'Decentraland'].slice(0, Math.floor(Math.random() * 4) + 1),
      expirationDate: Math.random() > 0.8 ? new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString() : undefined,
      upgradeability: Math.random() > 0.6
    },
    createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
    lastSaleAt: Math.random() > 0.5 ? new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString() : undefined
  }));
};

const generateVirtualBrands = (): VirtualBrand[] => {
  const categories = ['fashion', 'gaming', 'art', 'music', 'sports', 'tech', 'lifestyle'] as const;

  return Array.from({ length: 10 }, (_, i) => ({
    id: `brand-${i + 1}`,
    name: [
      'CyberLux', 'NeonStyle', 'PixelCraft', 'MetaFashion', 'DigitalDreams',
      'VirtualVogue', 'TechTrend', 'GameGear', 'ArtisticAura', 'MusicMetaverse'
    ][i],
    logo: `brand-logo-${i + 1}`,
    category: categories[i % categories.length],
    metaversePresence: {
      platforms: ['Horizon Worlds', 'VRChat', 'Roblox', 'Fortnite'].slice(0, Math.floor(Math.random() * 4) + 2),
      virtualStores: Math.floor(Math.random() * 10) + 1,
      experiences: Math.floor(Math.random() * 5) + 1,
      partnerships: [`Partner${i + 1}A`, `Partner${i + 1}B`]
    },
    digitalProducts: {
      totalItems: Math.floor(Math.random() * 500) + 50,
      nftCollections: Math.floor(Math.random() * 20) + 2,
      wearables: Math.floor(Math.random() * 200) + 20,
      accessories: Math.floor(Math.random() * 150) + 15,
      virtualRealEstate: Math.floor(Math.random() * 5) + 1
    },
    commerceMetrics: {
      monthlyRevenue: Math.random() * 500000 + 50000,
      totalSales: Math.floor(Math.random() * 10000) + 1000,
      averageOrderValue: Math.random() * 200 + 50,
      conversionRate: Math.random() * 10 + 5,
      customerRetention: Math.random() * 40 + 60
    },
    socialMetrics: {
      followers: Math.floor(Math.random() * 1000000) + 10000,
      engagement: Math.random() * 10 + 2,
      brandMentions: Math.floor(Math.random() * 5000) + 500,
      userGeneratedContent: Math.floor(Math.random() * 1000) + 100
    },
    marketPosition: {
      ranking: i + 1,
      marketShare: Math.random() * 15 + 2,
      competitorComparison: [
        { name: 'Competitor A', metric: 'Revenue', value: Math.random() * 1000000 + 100000 },
        { name: 'Competitor B', metric: 'Market Share', value: Math.random() * 10 + 5 }
      ]
    },
    createdAt: new Date(Date.now() - Math.random() * 730 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateCrossPlatformAnalytics = (): CrossPlatformAnalytics[] => {
  const platforms = ['Horizon Worlds', 'VRChat', 'Roblox', 'Fortnite', 'Minecraft', 'Decentraland'];
  const categories = ['Avatars', 'Wearables', 'Accessories', 'Virtual Real Estate', 'Experiences'];

  return Array.from({ length: 15 }, (_, i) => ({
    userId: `user-${i + 1}`,
    userName: `CrossUser${i + 1}`,
    platforms: platforms.slice(0, Math.floor(Math.random() * 4) + 2).map(platform => ({
      platform,
      timeSpent: Math.random() * 100 + 10,
      purchases: Math.floor(Math.random() * 20) + 1,
      totalSpent: Math.random() * 1000 + 100,
      favoriteCategories: categories.slice(0, Math.floor(Math.random() * 3) + 1),
      socialConnections: Math.floor(Math.random() * 500) + 10
    })),
    unifiedMetrics: {
      totalSpent: Math.random() * 2000 + 200,
      totalTimeSpent: Math.random() * 300 + 50,
      preferredPlatform: platforms[Math.floor(Math.random() * platforms.length)],
      crossPlatformSynergies: [
        { platform1: platforms[0], platform2: platforms[1], correlationScore: Math.random() * 100 },
        { platform1: platforms[1], platform2: platforms[2], correlationScore: Math.random() * 100 }
      ]
    },
    behaviorPatterns: {
      spendingPattern: ['consistent', 'seasonal', 'event_driven', 'impulsive'][Math.floor(Math.random() * 4)] as any,
      platformLoyalty: Math.random() * 100,
      categoryPreferences: {
        'Avatars': Math.random() * 100,
        'Wearables': Math.random() * 100,
        'Accessories': Math.random() * 100
      },
      priceRangePreference: {
        min: Math.random() * 50 + 5,
        max: Math.random() * 500 + 100,
        average: Math.random() * 200 + 50
      }
    },
    recommendations: {
      suggestedPlatforms: platforms.slice(0, Math.floor(Math.random() * 2) + 1),
      potentialSpending: Math.random() * 500 + 100,
      crossSellOpportunities: categories.slice(0, Math.floor(Math.random() * 3) + 1)
    }
  }));
};

const generateMetaverseInsights = (): MetaverseInsight[] => {
  const categories = ['market_trend', 'user_behavior', 'platform_performance', 'brand_opportunity', 'technology_adoption'] as const;
  const impacts = ['low', 'medium', 'high', 'critical'] as const;
  const platforms = ['Horizon Worlds', 'VRChat', 'Roblox', 'Fortnite', 'Minecraft', 'Decentraland', 'The Sandbox'];

  return Array.from({ length: 12 }, (_, i) => ({
    id: `insight-${i + 1}`,
    category: categories[i % categories.length],
    insight: [
      'Virtual real estate prices increased 400% in gaming metaverses',
      'Avatar customization drives 60% of metaverse commerce revenue',
      'Cross-platform digital asset compatibility boosts user engagement',
      'Social influence determines 70% of virtual fashion purchases',
      'NFT utility features increase retention by 250%',
      'Virtual concerts generate 300% more revenue than traditional events',
      'AI-powered avatars show 45% higher engagement rates',
      'Decentralized marketplaces gain 150% transaction growth',
      'Virtual brand stores outperform traditional e-commerce by 80%',
      'Haptic feedback increases purchase confidence by 65%',
      'Social shopping features double conversion rates',
      'Interoperable assets command 200% price premium'
    ][i],
    recommendation: [
      'Invest in virtual real estate across multiple gaming platforms',
      'Expand avatar customization options and accessories catalog',
      'Develop cross-platform asset compatibility standards',
      'Implement social proof and influencer integration features',
      'Add utility features to NFT collections to increase value',
      'Create virtual event spaces for branded experiences',
      'Integrate AI-powered avatar personalization systems',
      'Migrate to decentralized marketplace infrastructure',
      'Launch immersive virtual stores with 3D product displays',
      'Implement haptic feedback for virtual product interactions',
      'Add group buying and social shopping features',
      'Develop interoperable asset standards and protocols'
    ][i],
    impact: impacts[i % impacts.length],
    confidence: Math.random() * 30 + 70,
    timeframe: ['1-3 months', '3-6 months', '6-12 months', '1-2 years'][Math.floor(Math.random() * 4)],
    affectedPlatforms: platforms.slice(0, Math.floor(Math.random() * 4) + 2),
    relatedMetrics: [
      { metric: 'Transaction Volume', change: Math.random() * 200 - 50, period: 'Last 30 days' },
      { metric: 'User Engagement', change: Math.random() * 100 - 20, period: 'Last Quarter' }
    ],
    implementation: {
      steps: [
        'Market research and feasibility analysis',
        'Technical development and testing',
        'Beta launch with selected users',
        'Full rollout and optimization'
      ],
      resources: ['Development Team', 'Marketing Budget', 'Partnership Network'],
      timeline: ['2-4 weeks', '6-8 weeks', '1-2 months'][Math.floor(Math.random() * 3)]
    },
    expectedOutcome: [
      '400% increase in virtual real estate portfolio value',
      '60% boost in avatar-related revenue streams',
      '150% improvement in cross-platform user retention',
      '70% increase in social commerce conversion rates',
      '250% improvement in NFT holder engagement',
      '300% growth in virtual event attendance and revenue',
      '45% increase in AI-avatar user satisfaction scores',
      '150% growth in decentralized marketplace transactions',
      '80% improvement in virtual store performance metrics',
      '65% increase in haptic-enabled purchase completion',
      '100% improvement in social shopping conversion rates',
      '200% price premium for interoperable digital assets'
    ][i],
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const MetaverseCommerceTracker: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [metaverseFilter, setMetaverseFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [activeTab, setActiveTab] = useState('overview');

  // Generate mock data
  const transactions = useMemo(() => generateMetaverseTransactions(), []);
  const metaverseMetrics = useMemo(() => generateMetaverseMetrics(), []);
  const virtualMarketplaces = useMemo(() => generateVirtualMarketplaces(), []);
  const digitalAssets = useMemo(() => generateDigitalAssets(), []);
  const virtualBrands = useMemo(() => generateVirtualBrands(), []);
  const crossPlatformAnalytics = useMemo(() => generateCrossPlatformAnalytics(), []);
  const metaverseInsights = useMemo(() => generateMetaverseInsights(), []);

  // Filter and search logic
  const filteredTransactions = useMemo(() => {
    return transactions.filter(tx => {
      const matchesSearch = tx.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          tx.item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          tx.virtualWorld.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesMetaverse = metaverseFilter === 'all' || tx.metaverse === metaverseFilter;
      const matchesCategory = categoryFilter === 'all' || tx.item.category === categoryFilter;
      return matchesSearch && matchesMetaverse && matchesCategory;
    });
  }, [transactions, searchTerm, metaverseFilter, categoryFilter]);

  const getMetaverseIcon = (metaverse: string) => {
    switch (metaverse) {
      case 'horizon_worlds': return <Globe className="h-4 w-4" />;
      case 'vrchat': return <Users className="h-4 w-4" />;
      case 'roblox': return <Gamepad2 className="h-4 w-4" />;
      case 'fortnite': return <Zap className="h-4 w-4" />;
      case 'minecraft': return <Building2 className="h-4 w-4" />;
      case 'decentraland': return <Coins className="h-4 w-4" />;
      case 'sandbox': return <Gem className="h-4 w-4" />;
      case 'spatial': return <Sparkles className="h-4 w-4" />;
      default: return <Globe className="h-4 w-4" />;
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-500';
      case 'uncommon': return 'bg-green-500';
      case 'rare': return 'bg-blue-500';
      case 'epic': return 'bg-purple-500';
      case 'legendary': return 'bg-yellow-500';
      case 'mythic': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'purchase': return 'text-green-600';
      case 'sale': return 'text-blue-600';
      case 'trade': return 'text-purple-600';
      case 'auction': return 'text-orange-600';
      case 'gift': return 'text-pink-600';
      case 'rental': return 'text-cyan-600';
      case 'subscription': return 'text-indigo-600';
      default: return 'text-gray-600';
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    if (currency === 'usd') return `$${amount.toFixed(2)}`;
    return `${amount.toFixed(4)} ${currency.toUpperCase()}`;
  };

  return (
    <div className="p-6 space-y-6 bg-gradient-to-br from-cyan-50 via-blue-50 to-purple-50 min-h-screen">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-cyan-600 to-purple-600 bg-clip-text text-transparent">
            Metaverse Commerce Tracker
          </h1>
          <p className="text-gray-600 mt-2">Advanced analytics for virtual world transactions and digital asset commerce</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Refresh Data
          </Button>
          <Button className="flex items-center gap-2 bg-gradient-to-r from-cyan-500 to-purple-500">
            <Globe className="h-4 w-4" />
            Metaverse Analytics
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="marketplaces">Marketplaces</TabsTrigger>
          <TabsTrigger value="assets">Digital Assets</TabsTrigger>
          <TabsTrigger value="brands">Virtual Brands</TabsTrigger>
          <TabsTrigger value="analytics">Cross-Platform</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="bg-gradient-to-br from-cyan-500 to-cyan-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-cyan-100 text-sm font-medium">Total Volume (24h)</p>
                    <p className="text-3xl font-bold">
                      ${(transactions.reduce((sum, tx) => sum + tx.pricing.usdValue, 0) / 1000).toFixed(1)}k
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-cyan-200" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="bg-cyan-400 text-cyan-900">
                    +{Math.floor(Math.random() * 25 + 10)}% vs yesterday
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm font-medium">NFT Sales</p>
                    <p className="text-3xl font-bold">
                      {transactions.filter(tx => tx.item.isNFT).length}
                    </p>
                  </div>
                  <Gem className="h-8 w-8 text-purple-200" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="bg-purple-400 text-purple-900">
                    {Math.floor(transactions.filter(tx => tx.item.isNFT).length / transactions.length * 100)}% of total
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm font-medium">Active Metaverses</p>
                    <p className="text-3xl font-bold">
                      {Array.from(new Set(transactions.map(tx => tx.metaverse))).length}
                    </p>
                  </div>
                  <Globe className="h-8 w-8 text-blue-200" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="bg-blue-400 text-blue-900">
                    Cross-Platform
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm font-medium">Avg Order Value</p>
                    <p className="text-3xl font-bold">
                      ${Math.floor(transactions.reduce((sum, tx) => sum + tx.pricing.usdValue, 0) / transactions.length)}
                    </p>
                  </div>
                  <ShoppingBag className="h-8 w-8 text-green-200" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="bg-green-400 text-green-900">
                    Virtual Commerce
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Metaverse Transaction Volume
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={metaverseMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="totalVolume" stroke="#06b6d4" fill="#06b6d4" fillOpacity={0.6} />
                    <Area type="monotone" dataKey="totalTransactions" stroke="#8b5cf6" fill="#8b5cf6" fillOpacity={0.4} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Asset Category Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Array.from(new Set(transactions.map(tx => tx.item.category))).map((category) => {
                    const count = transactions.filter(tx => tx.item.category === category).length;
                    const percentage = (count / transactions.length) * 100;
                    const totalValue = transactions
                      .filter(tx => tx.item.category === category)
                      .reduce((sum, tx) => sum + tx.pricing.usdValue, 0);
                    
                    return (
                      <div key={category} className="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 rounded-full bg-gradient-to-r from-cyan-400 to-purple-400" />
                          <span className="font-medium capitalize">{category.replace('_', ' ')}</span>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">{count} items</p>
                          <p className="text-sm text-gray-600">${Math.floor(totalValue)}k</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Top Virtual Marketplaces
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {virtualMarketplaces
                    .filter(mp => mp.isActive)
                    .sort((a, b) => b.metrics.monthlyVolume - a.metrics.monthlyVolume)
                    .slice(0, 5)
                    .map((marketplace) => (
                    <div key={marketplace.id} className="flex items-center justify-between p-4 bg-gradient-to-r from-cyan-50 to-purple-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-cyan-400 to-purple-400 rounded-lg flex items-center justify-center">
                          <Store className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <p className="font-medium">{marketplace.name}</p>
                          <p className="text-sm text-gray-600">{marketplace.metaverse} • {marketplace.type}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-semibold text-green-600">
                          ${Math.floor(marketplace.metrics.monthlyVolume / 1000)}k
                        </p>
                        <p className="text-sm text-gray-600">Monthly Volume</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Crown className="h-5 w-5" />
                  Top Virtual Brands
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {virtualBrands
                    .sort((a, b) => b.commerceMetrics.monthlyRevenue - a.commerceMetrics.monthlyRevenue)
                    .slice(0, 6)
                    .map((brand, idx) => (
                    <div key={brand.id} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full flex items-center justify-center text-white font-bold text-sm">
                          {idx + 1}
                        </div>
                        <div>
                          <p className="font-medium text-sm">{brand.name}</p>
                          <p className="text-xs text-gray-600 capitalize">{brand.category}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-sm text-green-600">
                          ${Math.floor(brand.commerceMetrics.monthlyRevenue / 1000)}k
                        </p>
                        <p className="text-xs text-gray-600">Revenue</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search transactions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={metaverseFilter}
                onChange={(e) => setMetaverseFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Metaverses</option>
                <option value="horizon_worlds">Horizon Worlds</option>
                <option value="vrchat">VRChat</option>
                <option value="roblox">Roblox</option>
                <option value="fortnite">Fortnite</option>
                <option value="minecraft">Minecraft</option>
                <option value="decentraland">Decentraland</option>
                <option value="sandbox">The Sandbox</option>
                <option value="spatial">Spatial</option>
              </select>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Categories</option>
                <option value="avatar">Avatar</option>
                <option value="wearable">Wearable</option>
                <option value="accessory">Accessory</option>
                <option value="virtual_real_estate">Virtual Real Estate</option>
                <option value="digital_art">Digital Art</option>
                <option value="utility">Utility</option>
                <option value="experience">Experience</option>
                <option value="currency">Currency</option>
              </select>
            </div>
          </div>

          <div className="grid gap-4">
            <AnimatePresence>
              {filteredTransactions.map((transaction) => (
                <motion.div
                  key={transaction.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card className="hover:shadow-lg transition-shadow bg-gradient-to-r from-white to-gray-50">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <div className="w-10 h-10 bg-gradient-to-br from-cyan-400 to-purple-400 rounded-full flex items-center justify-center text-white font-bold">
                              {transaction.userName.slice(0, 2).toUpperCase()}
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold">{transaction.userName}</h3>
                              <p className="text-sm text-gray-600">{transaction.virtualWorld} • {transaction.location}</p>
                            </div>
                            <div className={`w-3 h-3 rounded-full ${getRarityColor(transaction.item.rarity)}`} />
                            <Badge variant="outline" className="flex items-center gap-1">
                              {getMetaverseIcon(transaction.metaverse)}
                              {transaction.metaverse.replace('_', ' ')}
                            </Badge>
                            <Badge variant="outline" className={getTransactionTypeColor(transaction.transactionType)}>
                              {transaction.transactionType}
                            </Badge>
                            {transaction.item.isNFT && (
                              <Badge variant="outline" className="border-purple-500 text-purple-700">
                                NFT
                              </Badge>
                            )}
                          </div>
                          
                          <div className="mb-4">
                            <h4 className="font-semibold text-gray-800">{transaction.item.name}</h4>
                            <p className="text-sm text-gray-600 capitalize">
                              {transaction.item.category.replace('_', ' ')} • {transaction.item.rarity}
                              {transaction.item.blockchain && ` • ${transaction.item.blockchain}`}
                            </p>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 text-sm">
                            <div>
                              <p className="text-gray-600">Price</p>
                              <p className="font-semibold text-green-600">
                                {formatCurrency(transaction.pricing.amount, transaction.pricing.currency)}
                              </p>
                              <p className="text-xs text-gray-500">${transaction.pricing.usdValue.toFixed(2)} USD</p>
                            </div>
                            <div>
                              <p className="text-gray-600">Payment Method</p>
                              <p className="font-medium capitalize">{transaction.paymentMethod.replace('_', ' ')}</p>
                              {transaction.pricing.gasFeesUSD && (
                                <p className="text-xs text-gray-500">+${transaction.pricing.gasFeesUSD.toFixed(2)} gas</p>
                              )}
                            </div>
                            <div>
                              <p className="text-gray-600">Discovery</p>
                              <p className="font-medium capitalize">{transaction.userJourney.discoverySource.replace('_', ' ')}</p>
                              <p className="text-xs text-gray-500">{transaction.userJourney.viewsBeforePurchase} views</p>
                            </div>
                            <div>
                              <p className="text-gray-600">Social Context</p>
                              <p className="font-medium">{transaction.socialContext.friendsOwned} friends own</p>
                              <p className="text-xs text-gray-500">
                                {transaction.socialContext.wasInfluencerRecommended ? 'Influencer rec.' : 'Organic'}
                              </p>
                            </div>
                          </div>

                          {transaction.seller && (
                            <div className="bg-gradient-to-r from-cyan-50 to-purple-50 p-3 rounded-lg">
                              <div className="flex items-center justify-between">
                                <div>
                                  <p className="font-medium text-cyan-700">Seller: {transaction.seller.name}</p>
                                  <p className="text-sm text-gray-600 capitalize">
                                    {transaction.seller.type} • {transaction.seller.reputation.toFixed(1)}⭐ rating
                                  </p>
                                </div>
                                {transaction.marketplace && (
                                  <Badge variant="outline" className="border-cyan-500 text-cyan-700">
                                    {transaction.marketplace}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          )}
                        </div>

                        <div className="flex flex-col gap-2">
                          <Button size="sm" variant="outline" className="flex items-center gap-1">
                            <Eye className="h-4 w-4" />
                            View Item
                          </Button>
                          <Button size="sm" variant="outline" className="flex items-center gap-1">
                            <ExternalLink className="h-4 w-4" />
                            Blockchain
                          </Button>
                          <Button size="sm" variant="outline">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </TabsContent>

        <TabsContent value="marketplaces" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Virtual Marketplaces</h2>
            <Button className="flex items-center gap-2 bg-gradient-to-r from-cyan-500 to-purple-500">
              <Plus className="h-4 w-4" />
              Add Marketplace
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {virtualMarketplaces.map((marketplace) => (
              <Card key={marketplace.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{marketplace.name}</CardTitle>
                    <Badge variant={marketplace.isActive ? "default" : "secondary"}>
                      {marketplace.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{marketplace.metaverse}</Badge>
                    <Badge variant="outline" className="capitalize">{marketplace.type.replace('_', ' ')}</Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Monthly Volume</p>
                      <p className="font-semibold text-green-600">${Math.floor(marketplace.metrics.monthlyVolume / 1000)}k</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Avg Order Value</p>
                      <p className="font-semibold">${Math.floor(marketplace.metrics.averageOrderValue)}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Active Buyers</p>
                      <p className="font-semibold">{marketplace.metrics.activeBuyers.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Commission</p>
                      <p className="font-semibold">{marketplace.metrics.commissionRate.toFixed(1)}%</p>
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">Categories:</p>
                    <div className="flex flex-wrap gap-1">
                      {marketplace.categories.map((category, idx) => (
                        <Badge key={idx} variant="secondary" className="text-xs">
                          {category}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {marketplace.technicalInfo.blockchain && (
                    <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-3 rounded-lg">
                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <div>
                          <p className="text-gray-600">Blockchain</p>
                          <p className="font-medium">{marketplace.technicalInfo.blockchain}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Features</p>
                          <div className="flex flex-col gap-1">
                            {marketplace.technicalInfo.royaltySupport && (
                              <span className="text-xs text-green-600">✓ Royalties</span>
                            )}
                            {marketplace.technicalInfo.fractionalOwnership && (
                              <span className="text-xs text-blue-600">✓ Fractions</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="flex justify-between text-sm">
                    <span>User Rating:</span>
                    <span className="font-medium text-yellow-600">
                      {marketplace.userRatings.overall.toFixed(1)} ⭐
                    </span>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <BarChart3 className="h-4 w-4 mr-1" />
                      Analytics
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="assets" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Digital Assets</h2>
            <div className="flex gap-2">
              <Button variant="outline" className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filter Assets
              </Button>
              <Button className="flex items-center gap-2">
                <Gem className="h-4 w-4" />
                Track Asset
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {digitalAssets.map((asset) => (
              <Card key={asset.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{asset.name}</CardTitle>
                    <div className="flex items-center gap-2">
                      {asset.isNFT && (
                        <Badge variant="outline" className="border-purple-500 text-purple-700">
                          NFT
                        </Badge>
                      )}
                      <Badge variant="outline" className={`capitalize ${getRarityColor(asset.rarity.toLowerCase())} text-white border-none`}>
                        {asset.rarity}
                      </Badge>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span>by {asset.creator}</span>
                    <span>•</span>
                    <span className="capitalize">{asset.category}</span>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Current Price</p>
                      <p className="font-semibold text-green-600">
                        {asset.pricing.currentPrice.toFixed(2)} {asset.pricing.currency}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600">Highest Sale</p>
                      <p className="font-semibold">{asset.pricing.highestSale.toFixed(2)} {asset.pricing.currency}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Supply</p>
                      <p className="font-semibold">{asset.ownership.totalSupply}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Owners</p>
                      <p className="font-semibold">{asset.ownership.currentOwners}</p>
                    </div>
                  </div>
                  
                  <div className="bg-gradient-to-r from-cyan-50 to-purple-50 p-3 rounded-lg">
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div>
                        <p className="text-gray-600">Views</p>
                        <p className="font-medium">{asset.engagement.views.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Likes</p>
                        <p className="font-medium">{asset.engagement.likes.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Uses</p>
                        <p className="font-medium">{asset.engagement.uses}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Shares</p>
                        <p className="font-medium">{asset.engagement.shares}</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">Platforms:</p>
                    <div className="flex flex-wrap gap-1">
                      {asset.utility.platformsSupported.map((platform, idx) => (
                        <Badge key={idx} variant="secondary" className="text-xs">
                          {platform}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">Marketplaces:</p>
                    <div className="flex flex-wrap gap-1">
                      {asset.marketplaces.map((marketplace, idx) => (
                        <Badge key={idx} variant="outline" className="text-xs">
                          {marketplace}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <TrendingUp className="h-4 w-4 mr-1" />
                      Price History
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="brands" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Virtual Brands</h2>
            <Button className="flex items-center gap-2">
              <Crown className="h-4 w-4" />
              Brand Analysis
            </Button>
          </div>

          <div className="grid gap-6">
            {virtualBrands.map((brand) => (
              <Card key={brand.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="w-16 h-16 bg-gradient-to-br from-cyan-400 to-purple-400 rounded-lg flex items-center justify-center">
                          <Crown className="h-8 w-8 text-white" />
                        </div>
                        <div>
                          <h3 className="text-2xl font-semibold">{brand.name}</h3>
                          <p className="text-gray-600 capitalize">{brand.category}</p>
                          <Badge variant="outline" className="mt-1">
                            #{brand.marketPosition.ranking} Brand
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                        <div>
                          <h4 className="font-semibold text-gray-700 mb-3">Commerce Metrics</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span>Monthly Revenue:</span>
                              <span className="font-medium text-green-600">
                                ${Math.floor(brand.commerceMetrics.monthlyRevenue / 1000)}k
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Total Sales:</span>
                              <span className="font-medium">{brand.commerceMetrics.totalSales.toLocaleString()}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Avg Order Value:</span>
                              <span className="font-medium">${Math.floor(brand.commerceMetrics.averageOrderValue)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Conversion Rate:</span>
                              <span className="font-medium">{brand.commerceMetrics.conversionRate.toFixed(1)}%</span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-semibold text-gray-700 mb-3">Digital Products</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span>Total Items:</span>
                              <span className="font-medium">{brand.digitalProducts.totalItems}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>NFT Collections:</span>
                              <span className="font-medium">{brand.digitalProducts.nftCollections}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Wearables:</span>
                              <span className="font-medium">{brand.digitalProducts.wearables}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Virtual Real Estate:</span>
                              <span className="font-medium">{brand.digitalProducts.virtualRealEstate}</span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-semibold text-gray-700 mb-3">Social Metrics</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span>Followers:</span>
                              <span className="font-medium">{(brand.socialMetrics.followers / 1000).toFixed(0)}k</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Engagement:</span>
                              <span className="font-medium">{brand.socialMetrics.engagement.toFixed(1)}%</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Brand Mentions:</span>
                              <span className="font-medium">{brand.socialMetrics.brandMentions.toLocaleString()}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>UGC Posts:</span>
                              <span className="font-medium">{brand.socialMetrics.userGeneratedContent}</span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-semibold text-gray-700 mb-3">Metaverse Presence</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span>Platforms:</span>
                              <span className="font-medium">{brand.metaversePresence.platforms.length}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Virtual Stores:</span>
                              <span className="font-medium">{brand.metaversePresence.virtualStores}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Experiences:</span>
                              <span className="font-medium">{brand.metaversePresence.experiences}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Market Share:</span>
                              <span className="font-medium">{brand.marketPosition.marketShare.toFixed(1)}%</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-gradient-to-r from-cyan-50 to-purple-50 p-4 rounded-lg">
                          <h4 className="font-semibold text-cyan-700 mb-2">Platform Presence</h4>
                          <div className="flex flex-wrap gap-2">
                            {brand.metaversePresence.platforms.map((platform, idx) => (
                              <Badge key={idx} variant="outline" className="border-cyan-500 text-cyan-700">
                                {platform}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg">
                          <h4 className="font-semibold text-purple-700 mb-2">Partnerships</h4>
                          <div className="flex flex-wrap gap-2">
                            {brand.metaversePresence.partnerships.map((partner, idx) => (
                              <Badge key={idx} variant="outline" className="border-purple-500 text-purple-700">
                                {partner}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col gap-2">
                      <Button size="sm" className="flex items-center gap-1">
                        <Eye className="h-4 w-4" />
                        View Brand
                      </Button>
                      <Button size="sm" variant="outline" className="flex items-center gap-1">
                        <BarChart3 className="h-4 w-4" />
                        Analytics
                      </Button>
                      <Button size="sm" variant="outline">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Cross-Platform Analytics</h2>
            <Button className="flex items-center gap-2">
              <Network className="h-4 w-4" />
              Platform Sync
            </Button>
          </div>

          <div className="grid gap-6">
            {crossPlatformAnalytics.slice(0, 8).map((analytics) => (
              <Card key={analytics.userId} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    {analytics.userName} - Cross-Platform Profile
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-green-700 mb-2">Unified Metrics</h4>
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <span>Total Spent:</span>
                          <span className="font-medium">${Math.floor(analytics.unifiedMetrics.totalSpent)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Total Time:</span>
                          <span className="font-medium">{Math.floor(analytics.unifiedMetrics.totalTimeSpent)}h</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Preferred Platform:</span>
                          <span className="font-medium">{analytics.unifiedMetrics.preferredPlatform}</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-blue-700 mb-2">Behavior Patterns</h4>
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <span>Spending Pattern:</span>
                          <span className="font-medium capitalize">{analytics.behaviorPatterns.spendingPattern.replace('_', ' ')}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Platform Loyalty:</span>
                          <span className="font-medium">{Math.floor(analytics.behaviorPatterns.platformLoyalty)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Avg Spend:</span>
                          <span className="font-medium">${Math.floor(analytics.behaviorPatterns.priceRangePreference.average)}</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-purple-700 mb-2">Recommendations</h4>
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <span>Potential Spending:</span>
                          <span className="font-medium text-green-600">${Math.floor(analytics.recommendations.potentialSpending)}</span>
                        </div>
                        <div className="text-xs text-gray-600 mt-2">
                          Suggested: {analytics.recommendations.suggestedPlatforms.join(', ')}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-700 mb-3">Platform Activity</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {analytics.platforms.map((platform, idx) => (
                        <div key={idx} className="bg-gray-50 p-4 rounded-lg">
                          <div className="flex items-center gap-2 mb-2">
                            <div className="w-3 h-3 bg-gradient-to-r from-cyan-400 to-purple-400 rounded-full" />
                            <span className="font-medium">{platform.platform}</span>
                          </div>
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div>
                              <p className="text-gray-600">Time Spent</p>
                              <p className="font-medium">{Math.floor(platform.timeSpent)}h</p>
                            </div>
                            <div>
                              <p className="text-gray-600">Purchases</p>
                              <p className="font-medium">{platform.purchases}</p>
                            </div>
                            <div>
                              <p className="text-gray-600">Total Spent</p>
                              <p className="font-medium text-green-600">${Math.floor(platform.totalSpent)}</p>
                            </div>
                            <div>
                              <p className="text-gray-600">Connections</p>
                              <p className="font-medium">{platform.socialConnections}</p>
                            </div>
                          </div>
                          <div className="mt-2">
                            <p className="text-xs text-gray-600">Favorite Categories:</p>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {platform.favoriteCategories.map((cat, catIdx) => (
                                <Badge key={catIdx} variant="secondary" className="text-xs">
                                  {cat}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Metaverse Commerce Insights</h2>
            <Button className="flex items-center gap-2 bg-gradient-to-r from-cyan-500 to-purple-500">
              <Brain className="h-4 w-4" />
              Generate Insights
            </Button>
          </div>

          <div className="grid gap-4">
            {metaverseInsights.map((insight) => (
              <Card key={insight.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <Badge variant="outline" className="capitalize">
                          {insight.category.replace('_', ' ')}
                        </Badge>
                        <Badge variant={
                          insight.impact === 'critical' ? 'destructive' :
                          insight.impact === 'high' ? 'default' :
                          insight.impact === 'medium' ? 'secondary' :
                          'outline'
                        }>
                          {insight.impact} impact
                        </Badge>
                        <Badge variant="outline">{Math.floor(insight.confidence)}% confidence</Badge>
                        <Badge variant="outline" className="border-cyan-500 text-cyan-700">
                          {insight.timeframe}
                        </Badge>
                      </div>
                      <h3 className="text-lg font-semibold mb-2">{insight.insight}</h3>
                      <p className="text-gray-600 mb-4">{insight.recommendation}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <h4 className="font-semibold text-gray-700 mb-2">Affected Platforms</h4>
                          <div className="flex flex-wrap gap-1">
                            {insight.affectedPlatforms.map((platform, idx) => (
                              <Badge key={idx} variant="secondary" className="text-xs">
                                {platform}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h4 className="font-semibold text-gray-700 mb-2">Related Metrics</h4>
                          <div className="space-y-1">
                            {insight.relatedMetrics.map((metric, idx) => (
                              <div key={idx} className="flex justify-between text-sm">
                                <span>{metric.metric}:</span>
                                <span className={`font-medium ${metric.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                  {metric.change >= 0 ? '+' : ''}{metric.change.toFixed(1)}%
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      <div className="bg-gradient-to-r from-cyan-50 to-purple-50 p-4 rounded-lg">
                        <h4 className="font-semibold text-cyan-700 mb-2">Implementation Plan</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-gray-600 font-medium mb-1">Timeline:</p>
                            <p>{insight.implementation.timeline}</p>
                          </div>
                          <div>
                            <p className="text-gray-600 font-medium mb-1">Resources:</p>
                            <p>{insight.implementation.resources.join(', ')}</p>
                          </div>
                        </div>
                        <div className="mt-3">
                          <p className="text-gray-600 font-medium mb-1">Expected Outcome:</p>
                          <p className="text-green-600 font-medium">{insight.expectedOutcome}</p>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button size="sm" className="flex items-center gap-1">
                        <Target className="h-4 w-4" />
                        Implement
                      </Button>
                      <Button variant="outline" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MetaverseCommerceTracker;