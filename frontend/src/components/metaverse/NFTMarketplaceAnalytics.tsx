/**
 * NFT Marketplace Analytics
 * Advanced analytics for non-fungible token trading, collection performance, and market dynamics
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Gem,
  Coins,
  Crown,
  Star,
  Award,
  Trophy,
  Sparkles,
  Zap,
  TrendingUp,
  TrendingDown,
  BarChart3,
  LineChart,
  PieChart,
  Activity,
  Users,
  Eye,
  Heart,
  Share,
  DollarSign,
  CreditCard,
  Wallet,
  ShoppingCart,
  Target,
  Clock,
  Calendar,
  Globe,
  Network,
  Layers,
  Box,
  Cube,
  Image,
  Video,
  Music,
  Palette,
  Brush,
  Camera,
  Gamepad2,
  Building2,
  Home,
  Store,
  Factory,
  Archive,
  Database,
  Server,
  Cloud,
  Cpu,
  Brain,
  Shield,
  Lock,
  Key,
  Link2,
  ExternalLink,
  Download,
  Upload,
  Share2,
  MessageCircle,
  ThumbsUp,
  ThumbsDown,
  Flag,
  Settings,
  RefreshCw,
  Search,
  Filter,
  Plus,
  Minus,
  Edit,
  Trash,
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  ArrowLeft,
  CheckCircle,
  AlertTriangle,
  Info,
  HelpCircle,
  BookOpen,
  FileText,
  File,
  Folder,
  Save,
  Mail,
  Phone,
  Bell,
  BellRing,
  Volume2,
  Mic,
  Speaker,
  Headphones,
  Monitor,
  Smartphone,
  Tablet,
  Watch,
  Laptop,
  Wifi,
  Signal,
  Battery,
  Power,
  Lightbulb,
  Rocket,
  Wand2,
  Move3d,
  Navigation,
  Compass,
  MapPin,
  Route,
  TreePine,
  Mountain,
  Waves,
  Sun,
  Moon,
  Coffee,
  Utensils,
  Car,
  Plane,
  Shirt,
  Focus,
  Crosshair,
  Gauge
} from 'lucide-react';
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart as RechartsBarChart, Bar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ScatterChart, Scatter, ComposedChart, Cell, Treemap, Sankey, PieChart as RechartsPieChart, Pie } from 'recharts';

// TypeScript interfaces for NFT marketplace analytics
interface NFTCollection {
  id: string;
  name: string;
  symbol: string;
  description: string;
  creatorAddress: string;
  creatorName: string;
  contractAddress: string;
  blockchain: 'Ethereum' | 'Polygon' | 'Solana' | 'Binance Smart Chain' | 'Avalanche' | 'Flow' | 'Tezos';
  category: 'Art' | 'Gaming' | 'Collectibles' | 'Photography' | 'Music' | 'Video' | 'Utility' | 'Domain Names' | 'Virtual Real Estate' | 'Sports';
  totalSupply: number;
  mintedSupply: number;
  mintPrice: number;
  floorPrice: number;
  averagePrice: number;
  totalVolume: number;
  marketCap: number;
  holderCount: number;
  uniqueOwners: number;
  ownershipDistribution: { range: string; count: number; percentage: number }[];
  metadata: {
    traits: { trait_type: string; values: { value: string; rarity: number; count: number }[] }[];
    rarityDistribution: { rarity: string; count: number; floorPrice: number }[];
    mediaTypes: { type: string; count: number }[];
    fileFormats: string[];
    averageFileSize: number;
    ipfsUsage: number;
    onChainMetadata: number;
  };
  metrics: {
    dailyVolume: number;
    weeklyVolume: number;
    monthlyVolume: number;
    dailyTransactions: number;
    weeklyTransactions: number;
    monthlyTransactions: number;
    averageHoldTime: number;
    velocityScore: number;
    liquidityScore: number;
    volatilityIndex: number;
  };
  socialMetrics: {
    discordMembers: number;
    twitterFollowers: number;
    websiteTraffic: number;
    socialSentiment: number;
    communityActivity: number;
    influencerMentions: number;
    mediaApperances: number;
  };
  utilityFeatures: {
    stakingRewards: boolean;
    governanceRights: boolean;
    accessRights: boolean;
    gameUtility: boolean;
    physicalRedemption: boolean;
    commercialRights: boolean;
    royaltySharing: boolean;
  };
  roadmapProgress: {
    phase: string;
    completion: number;
    milestones: { name: string; status: 'completed' | 'in_progress' | 'planned' }[];
  }[];
  createdAt: string;
  lastUpdated: string;
}

interface NFTTransaction {
  id: string;
  tokenId: string;
  collectionId: string;
  transactionType: 'mint' | 'sale' | 'transfer' | 'burn' | 'stake' | 'unstake' | 'bid' | 'offer';
  fromAddress: string;
  toAddress: string;
  price: number;
  currency: string;
  usdValue: number;
  marketplace: string;
  gasPrice: number;
  gasUsed: number;
  transactionHash: string;
  blockNumber: number;
  timestamp: string;
  metadata: {
    isBundle: boolean;
    bundleSize?: number;
    isAuction: boolean;
    auctionEndTime?: string;
    royaltyAmount: number;
    royaltyRecipient: string;
    platformFee: number;
  };
  qualityMetrics: {
    rarity: number;
    traitScore: number;
    artistReputation: number;
    historicalPerformance: number;
    communityValue: number;
  };
}

interface MarketplaceMetrics {
  timestamp: string;
  totalVolume: number;
  totalTransactions: number;
  uniqueTraders: number;
  newListings: number;
  activeListings: number;
  salesCount: number;
  averageSalePrice: number;
  medianSalePrice: number;
  floorPriceIndex: number;
  marketCapitalization: number;
  liquidityIndex: number;
  priceVolatility: number;
  washTradingScore: number;
  gasUsageTotal: number;
  crossChainActivity: number;
  institutionalVolume: number;
  retailVolume: number;
}

interface TraderProfile {
  address: string;
  alias: string;
  traderType: 'whale' | 'collector' | 'flipper' | 'hodler' | 'creator' | 'institutional' | 'retail';
  portfolioValue: number;
  totalTransactions: number;
  totalVolume: number;
  profitLoss: number;
  winRate: number;
  averageHoldTime: number;
  collections: {
    collectionId: string;
    tokensOwned: number;
    totalSpent: number;
    currentValue: number;
    profitLoss: number;
  }[];
  tradingPatterns: {
    preferredMarketplaces: string[];
    averageTransactionSize: number;
    tradingFrequency: string;
    riskTolerance: number;
    diversificationIndex: number;
    seasonality: { month: string; activity: number }[];
  };
  socialMetrics: {
    followersCount: number;
    influenceScore: number;
    alphaCallsAccuracy: number;
    communityContributions: number;
    reputationScore: number;
  };
  behaviorAnalysis: {
    decisionMakingSpeed: 'fast' | 'moderate' | 'slow';
    marketSentiment: 'bullish' | 'bearish' | 'neutral';
    riskAppetite: 'conservative' | 'moderate' | 'aggressive';
    informationSources: string[];
    tradingHours: number[];
    deviceUsage: { device: string; percentage: number }[];
  };
}

interface NFTTrend {
  id: string;
  category: 'emerging_collection' | 'price_movement' | 'volume_spike' | 'new_creator' | 'utility_adoption' | 'cross_chain_activity';
  title: string;
  description: string;
  trendStrength: number;
  timeframe: string;
  affectedCollections: string[];
  keyMetrics: { metric: string; value: number; change: number }[];
  drivers: string[];
  implications: string[];
  actionableInsights: string[];
  confidence: number;
  impact: 'low' | 'medium' | 'high' | 'critical';
  createdAt: string;
}

interface MarketInsight {
  id: string;
  category: 'market_opportunity' | 'risk_assessment' | 'collection_analysis' | 'trader_behavior' | 'technology_trend';
  insight: string;
  dataPoints: number;
  confidence: number;
  impact: 'low' | 'medium' | 'high' | 'critical';
  recommendation: string;
  implementation: {
    complexity: 'simple' | 'moderate' | 'complex';
    timeframe: string;
    resources: string[];
    expectedOutcome: string;
    riskFactors: string[];
  };
  affectedSegments: string[];
  businessValue: string;
  priority: number;
  createdAt: string;
}

interface RarityAnalysis {
  collectionId: string;
  tokenId: string;
  rarityRank: number;
  rarityScore: number;
  traitAnalysis: {
    trait_type: string;
    value: string;
    rarity: number;
    contribution: number;
  }[];
  marketPerformance: {
    lastSalePrice: number;
    estimatedValue: number;
    priceAppreciation: number;
    liquidityRating: number;
    demandScore: number;
  };
  ownershipHistory: {
    address: string;
    acquisitionPrice: number;
    holdDuration: number;
    acquisitionDate: string;
  }[];
  utilityValue: {
    stakingRewards: number;
    accessRights: string[];
    gameplayValue: number;
    socialStatus: number;
    commercialRights: number;
  };
}

// Mock data generation functions
const generateNFTCollections = (): NFTCollection[] => {
  const blockchains = ['Ethereum', 'Polygon', 'Solana', 'Binance Smart Chain', 'Avalanche', 'Flow', 'Tezos'] as const;
  const categories = ['Art', 'Gaming', 'Collectibles', 'Photography', 'Music', 'Video', 'Utility', 'Domain Names', 'Virtual Real Estate', 'Sports'] as const;
  const names = ['CryptoPunks Elite', 'Bored Apes Evolved', 'Art Blocks Genesis', 'Axie Infinity Origins', 'NBA Top Shot Moments', 'Decentraland Estates', 'ENS Domains Premium', 'The Sandbox Assets', 'SuperRare Masterpieces', 'Foundation Curated'];
  
  return Array.from({ length: 10 }, (_, i) => ({
    id: `collection-${i + 1}`,
    name: names[i] || `Collection ${i + 1}`,
    symbol: `COL${i + 1}`,
    description: `Premium NFT collection featuring unique digital assets with exceptional utility and artistic value.`,
    creatorAddress: `0x${Math.random().toString(16).substr(2, 40)}`,
    creatorName: `Artist ${i + 1}`,
    contractAddress: `0x${Math.random().toString(16).substr(2, 40)}`,
    blockchain: blockchains[i % blockchains.length],
    category: categories[i % categories.length],
    totalSupply: Math.floor(Math.random() * 10000) + 1000,
    mintedSupply: Math.floor(Math.random() * 8000) + 800,
    mintPrice: Math.random() * 2 + 0.1,
    floorPrice: Math.random() * 5 + 0.5,
    averagePrice: Math.random() * 10 + 2,
    totalVolume: Math.floor(Math.random() * 50000) + 10000,
    marketCap: Math.floor(Math.random() * 100000000) + 10000000,
    holderCount: Math.floor(Math.random() * 5000) + 500,
    uniqueOwners: Math.floor(Math.random() * 4000) + 400,
    ownershipDistribution: [
      { range: '1', count: Math.floor(Math.random() * 2000) + 1000, percentage: 70 },
      { range: '2-5', count: Math.floor(Math.random() * 800) + 200, percentage: 20 },
      { range: '6-10', count: Math.floor(Math.random() * 200) + 50, percentage: 7 },
      { range: '11+', count: Math.floor(Math.random() * 100) + 20, percentage: 3 }
    ],
    metadata: {
      traits: [
        {
          trait_type: 'Background',
          values: [
            { value: 'Rare', rarity: 5, count: 50 },
            { value: 'Common', rarity: 70, count: 700 },
            { value: 'Legendary', rarity: 1, count: 10 }
          ]
        },
        {
          trait_type: 'Eyes',
          values: [
            { value: 'Blue', rarity: 30, count: 300 },
            { value: 'Green', rarity: 25, count: 250 },
            { value: 'Gold', rarity: 2, count: 20 }
          ]
        }
      ],
      rarityDistribution: [
        { rarity: 'Common', count: Math.floor(Math.random() * 500) + 200, floorPrice: Math.random() * 2 + 0.5 },
        { rarity: 'Rare', count: Math.floor(Math.random() * 200) + 50, floorPrice: Math.random() * 5 + 2 },
        { rarity: 'Epic', count: Math.floor(Math.random() * 50) + 10, floorPrice: Math.random() * 10 + 5 },
        { rarity: 'Legendary', count: Math.floor(Math.random() * 10) + 1, floorPrice: Math.random() * 50 + 20 }
      ],
      mediaTypes: [
        { type: 'Image', count: Math.floor(Math.random() * 800) + 700 },
        { type: 'Video', count: Math.floor(Math.random() * 200) + 100 },
        { type: 'Audio', count: Math.floor(Math.random() * 100) + 50 },
        { type: '3D Model', count: Math.floor(Math.random() * 150) + 75 }
      ],
      fileFormats: ['PNG', 'JPG', 'GIF', 'MP4', 'GLB'].slice(0, Math.floor(Math.random() * 4) + 2),
      averageFileSize: Math.random() * 50 + 5, // MB
      ipfsUsage: Math.random() * 100,
      onChainMetadata: Math.random() * 100
    },
    metrics: {
      dailyVolume: Math.floor(Math.random() * 10000) + 1000,
      weeklyVolume: Math.floor(Math.random() * 50000) + 5000,
      monthlyVolume: Math.floor(Math.random() * 200000) + 20000,
      dailyTransactions: Math.floor(Math.random() * 500) + 50,
      weeklyTransactions: Math.floor(Math.random() * 2000) + 200,
      monthlyTransactions: Math.floor(Math.random() * 8000) + 800,
      averageHoldTime: Math.random() * 365 + 30, // days
      velocityScore: Math.random() * 100,
      liquidityScore: Math.random() * 100,
      volatilityIndex: Math.random() * 100
    },
    socialMetrics: {
      discordMembers: Math.floor(Math.random() * 100000) + 10000,
      twitterFollowers: Math.floor(Math.random() * 500000) + 50000,
      websiteTraffic: Math.floor(Math.random() * 1000000) + 100000,
      socialSentiment: Math.random() * 100,
      communityActivity: Math.random() * 100,
      influencerMentions: Math.floor(Math.random() * 1000) + 100,
      mediaApperances: Math.floor(Math.random() * 100) + 10
    },
    utilityFeatures: {
      stakingRewards: Math.random() > 0.5,
      governanceRights: Math.random() > 0.6,
      accessRights: Math.random() > 0.4,
      gameUtility: Math.random() > 0.7,
      physicalRedemption: Math.random() > 0.8,
      commercialRights: Math.random() > 0.9,
      royaltySharing: Math.random() > 0.85
    },
    roadmapProgress: [
      {
        phase: 'Phase 1: Launch',
        completion: 100,
        milestones: [
          { name: 'Contract Deployment', status: 'completed' },
          { name: 'Initial Mint', status: 'completed' },
          { name: 'Marketplace Listing', status: 'completed' }
        ]
      },
      {
        phase: 'Phase 2: Utility',
        completion: Math.random() * 100,
        milestones: [
          { name: 'Staking Implementation', status: Math.random() > 0.5 ? 'completed' : 'in_progress' },
          { name: 'Game Integration', status: Math.random() > 0.7 ? 'completed' : 'planned' },
          { name: 'Governance Launch', status: 'planned' }
        ]
      }
    ],
    createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
    lastUpdated: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateMarketplaceMetrics = (): MarketplaceMetrics[] => {
  return Array.from({ length: 24 }, (_, i) => ({
    timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString().split('T')[1].split(':')[0] + ':00',
    totalVolume: Math.floor(Math.random() * 1000000) + 200000,
    totalTransactions: Math.floor(Math.random() * 5000) + 500,
    uniqueTraders: Math.floor(Math.random() * 2000) + 200,
    newListings: Math.floor(Math.random() * 1000) + 100,
    activeListings: Math.floor(Math.random() * 10000) + 2000,
    salesCount: Math.floor(Math.random() * 800) + 100,
    averageSalePrice: Math.random() * 10 + 1,
    medianSalePrice: Math.random() * 5 + 0.5,
    floorPriceIndex: Math.random() * 200 + 100,
    marketCapitalization: Math.floor(Math.random() * 100000000) + 50000000,
    liquidityIndex: Math.random() * 100,
    priceVolatility: Math.random() * 50 + 10,
    washTradingScore: Math.random() * 20,
    gasUsageTotal: Math.floor(Math.random() * 1000) + 200,
    crossChainActivity: Math.random() * 100,
    institutionalVolume: Math.floor(Math.random() * 500000) + 100000,
    retailVolume: Math.floor(Math.random() * 500000) + 100000
  }));
};

const generateTraderProfiles = (): TraderProfile[] => {
  const traderTypes = ['whale', 'collector', 'flipper', 'hodler', 'creator', 'institutional', 'retail'] as const;
  
  return Array.from({ length: 8 }, (_, i) => ({
    address: `0x${Math.random().toString(16).substr(2, 40)}`,
    alias: `Trader${i + 1}`,
    traderType: traderTypes[i % traderTypes.length],
    portfolioValue: Math.floor(Math.random() * 10000000) + 100000,
    totalTransactions: Math.floor(Math.random() * 1000) + 50,
    totalVolume: Math.floor(Math.random() * 5000000) + 500000,
    profitLoss: (Math.random() - 0.5) * 1000000,
    winRate: Math.random() * 50 + 50,
    averageHoldTime: Math.random() * 365 + 30,
    collections: Array.from({ length: Math.floor(Math.random() * 5) + 1 }, (_, j) => ({
      collectionId: `collection-${j + 1}`,
      tokensOwned: Math.floor(Math.random() * 50) + 1,
      totalSpent: Math.floor(Math.random() * 100000) + 10000,
      currentValue: Math.floor(Math.random() * 150000) + 5000,
      profitLoss: (Math.random() - 0.5) * 50000
    })),
    tradingPatterns: {
      preferredMarketplaces: ['OpenSea', 'LooksRare', 'Foundation', 'SuperRare'].slice(0, Math.floor(Math.random() * 3) + 1),
      averageTransactionSize: Math.random() * 10 + 1,
      tradingFrequency: ['Daily', 'Weekly', 'Monthly'][Math.floor(Math.random() * 3)],
      riskTolerance: Math.random() * 100,
      diversificationIndex: Math.random() * 100,
      seasonality: Array.from({ length: 12 }, (_, month) => ({
        month: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][month],
        activity: Math.random() * 100
      }))
    },
    socialMetrics: {
      followersCount: Math.floor(Math.random() * 100000) + 1000,
      influenceScore: Math.random() * 100,
      alphaCallsAccuracy: Math.random() * 100,
      communityContributions: Math.floor(Math.random() * 1000) + 10,
      reputationScore: Math.random() * 100
    },
    behaviorAnalysis: {
      decisionMakingSpeed: ['fast', 'moderate', 'slow'][Math.floor(Math.random() * 3)] as any,
      marketSentiment: ['bullish', 'bearish', 'neutral'][Math.floor(Math.random() * 3)] as any,
      riskAppetite: ['conservative', 'moderate', 'aggressive'][Math.floor(Math.random() * 3)] as any,
      informationSources: ['Twitter', 'Discord', 'Reddit', 'Telegram', 'Alpha Groups'].slice(0, Math.floor(Math.random() * 4) + 1),
      tradingHours: Array.from({ length: Math.floor(Math.random() * 8) + 4 }, () => Math.floor(Math.random() * 24)),
      deviceUsage: [
        { device: 'Desktop', percentage: Math.random() * 60 + 40 },
        { device: 'Mobile', percentage: Math.random() * 40 + 20 },
        { device: 'Tablet', percentage: Math.random() * 20 + 5 }
      ]
    }
  }));
};

const generateNFTTrends = (): NFTTrend[] => {
  const categories = ['emerging_collection', 'price_movement', 'volume_spike', 'new_creator', 'utility_adoption', 'cross_chain_activity'] as const;
  
  return Array.from({ length: 8 }, (_, i) => ({
    id: `trend-${i + 1}`,
    category: categories[i % categories.length],
    title: [
      'AI-Generated Art Collections Surge 340% in Trading Volume',
      'Gaming NFTs Show Strong Utility Adoption with 67% Increase',
      'Cross-Chain Bridge Activity Reaches All-Time High',
      'Celebrity Artist Collaborations Drive Premium Segment Growth',
      'Fractionalized NFT Ownership Gains Mainstream Adoption',
      'Environmental-Themed Collections See 200% Floor Price Increase',
      'Music NFT Royalty Sharing Creates New Revenue Streams',
      'Virtual Real Estate in Metaverse Platforms Appreciates 150%'
    ][i],
    description: [
      'Machine learning and AI-generated art collections are experiencing unprecedented demand as collectors seek unique algorithmic creations.',
      'Play-to-earn and GameFi ecosystems are driving real utility for gaming NFTs beyond speculative trading.',
      'Multi-chain interoperability solutions are enabling seamless NFT transfers between major blockchains.',
      'High-profile artists entering the NFT space are creating premium market segments with sustained demand.',
      'Fractional ownership platforms are democratizing access to high-value NFT collections and blue-chip assets.',
      'Climate-conscious collectors are driving demand for carbon-negative and environmentally sustainable NFT projects.',
      'Musicians are leveraging NFTs to create direct fan engagement and recurring royalty income streams.',
      'Virtual worlds and metaverse platforms are creating digital land scarcity and development opportunities.'
    ][i],
    trendStrength: Math.random() * 40 + 60,
    timeframe: ['24 hours', '7 days', '30 days'][Math.floor(Math.random() * 3)],
    affectedCollections: [`collection-${(i % 3) + 1}`, `collection-${(i % 4) + 2}`],
    keyMetrics: [
      { metric: 'Volume Change', value: Math.random() * 500 + 100, change: Math.random() * 200 - 100 },
      { metric: 'Price Change', value: Math.random() * 100 + 50, change: Math.random() * 100 - 50 },
      { metric: 'Active Traders', value: Math.floor(Math.random() * 1000) + 200, change: Math.random() * 100 - 50 }
    ],
    drivers: [
      ['Celebrity endorsement', 'Viral social media campaign', 'Utility announcement'],
      ['Game launch', 'Partnership announcement', 'Staking rewards'],
      ['Technical breakthrough', 'Cross-chain integration', 'New marketplace listing'],
      ['Artist collaboration', 'Brand partnership', 'Limited edition release'],
      ['Platform innovation', 'Regulatory clarity', 'Institutional adoption'],
      ['ESG focus', 'Carbon neutrality', 'Environmental initiative'],
      ['Royalty innovation', 'Fan engagement', 'Music industry adoption'],
      ['Metaverse expansion', 'Virtual events', 'Digital land development']
    ][i],
    implications: [
      ['Increased market sophistication', 'Technology adoption acceleration', 'Creator economy expansion'],
      ['Utility-driven valuation models', 'Gaming industry integration', 'Play-to-earn growth'],
      ['Multi-chain ecosystem maturity', 'Reduced transaction costs', 'Enhanced liquidity'],
      ['Premium market segment creation', 'Traditional art market bridge', 'Cultural mainstream adoption'],
      ['Democratized investment access', 'Liquidity improvement', 'Risk distribution'],
      ['Sustainable market development', 'ESG compliance priority', 'Long-term value focus'],
      ['New revenue models', 'Fan monetization', 'Industry disruption'],
      ['Digital real estate maturity', 'Virtual economy growth', 'Metaverse infrastructure']
    ][i],
    actionableInsights: [
      ['Monitor AI art generator technologies', 'Track algorithmic creativity trends', 'Evaluate long-term artistic value'],
      ['Assess gaming utility implementation', 'Track play-to-earn sustainability', 'Monitor ecosystem development'],
      ['Leverage cross-chain opportunities', 'Optimize gas cost strategies', 'Explore new blockchain adoption'],
      ['Identify emerging artist collaborations', 'Track celebrity NFT performance', 'Evaluate brand partnerships'],
      ['Explore fractional ownership platforms', 'Assess liquidity improvement', 'Consider portfolio diversification'],
      ['Integrate ESG criteria in selection', 'Track sustainability metrics', 'Support environmental initiatives'],
      ['Evaluate music NFT platforms', 'Assess royalty mechanisms', 'Track fan engagement metrics'],
      ['Monitor metaverse development', 'Assess virtual land utility', 'Track digital economy growth']
    ][i],
    confidence: Math.random() * 30 + 70,
    impact: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateMarketInsights = (): MarketInsight[] => {
  const categories = ['market_opportunity', 'risk_assessment', 'collection_analysis', 'trader_behavior', 'technology_trend'] as const;
  
  return Array.from({ length: 10 }, (_, i) => ({
    id: `insight-${i + 1}`,
    category: categories[i % categories.length],
    insight: [
      'Blue-chip collections show 73% correlation with traditional art market cycles',
      'Wash trading detection algorithms identify 15% of high-volume transactions as suspicious',
      'Collections with active Discord communities maintain 23% higher floor prices',
      'Institutional buyers prefer collections with established provenance and utility features',
      'Cross-chain NFT bridges reduce transaction costs by 45% while maintaining security',
      'AI-generated collections require new valuation models beyond traditional rarity metrics',
      'Gaming NFTs with proven utility maintain value during market downturns 67% better',
      'Environmental sustainability features correlate with 31% premium in long-term holdings',
      'Fractionalized ownership increases small investor participation by 234%',
      'Celebrity-endorsed collections show 89% correlation with social media engagement'
    ][i],
    dataPoints: Math.floor(Math.random() * 100000) + 10000,
    confidence: Math.random() * 30 + 70,
    impact: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
    recommendation: [
      'Diversify NFT portfolio with traditional art market correlation analysis',
      'Implement advanced wash trading detection in trading algorithms',
      'Prioritize collections with strong community engagement metrics',
      'Focus on utility-driven collections for institutional investment strategies',
      'Leverage cross-chain infrastructure for cost optimization',
      'Develop AI-specific valuation frameworks for algorithmic art',
      'Weight gaming utility heavily in bear market portfolio allocation',
      'Integrate ESG criteria into NFT investment decision frameworks',
      'Explore fractional ownership platforms for portfolio diversification',
      'Monitor social sentiment as leading indicator for celebrity collections'
    ][i],
    implementation: {
      complexity: ['simple', 'moderate', 'complex'][Math.floor(Math.random() * 3)] as any,
      timeframe: ['2-4 weeks', '1-3 months', '3-6 months', '6-12 months'][Math.floor(Math.random() * 4)],
      resources: ['Analytics Team', 'Development Resources', 'Market Research', 'Community Management'].slice(0, Math.floor(Math.random() * 3) + 1),
      expectedOutcome: `${Math.floor(Math.random() * 50) + 10}% improvement in trading performance`,
      riskFactors: ['Market volatility', 'Regulatory changes', 'Technology adoption', 'Community sentiment'].slice(0, Math.floor(Math.random() * 3) + 1)
    },
    affectedSegments: ['Collectors', 'Traders', 'Creators', 'Investors', 'Institutions'].slice(0, Math.floor(Math.random() * 4) + 1),
    businessValue: `$${Math.floor(Math.random() * 1000000) + 100000} potential annual impact`,
    priority: Math.floor(Math.random() * 10) + 1,
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const NFTMarketplaceAnalytics: React.FC = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('24h');
  const [selectedBlockchain, setSelectedBlockchain] = useState('all');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');

  // Generated data
  const nftCollections = useMemo(() => generateNFTCollections(), []);
  const marketplaceMetrics = useMemo(() => generateMarketplaceMetrics(), []);
  const traderProfiles = useMemo(() => generateTraderProfiles(), []);
  const nftTrends = useMemo(() => generateNFTTrends(), []);
  const marketInsights = useMemo(() => generateMarketInsights(), []);

  // Filtering and processing
  const filteredCollections = useMemo(() => {
    return nftCollections.filter(collection => 
      (selectedBlockchain === 'all' || collection.blockchain === selectedBlockchain) &&
      (selectedCategory === 'all' || collection.category === selectedCategory) &&
      (collection.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
       collection.creatorName.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [nftCollections, selectedBlockchain, selectedCategory, searchTerm]);

  const filteredInsights = useMemo(() => {
    return marketInsights.filter(insight => 
      insight.insight.toLowerCase().includes(searchTerm.toLowerCase()) ||
      insight.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [marketInsights, searchTerm]);

  const aggregatedMetrics = useMemo(() => {
    const collections = filteredCollections;
    
    return {
      totalCollections: collections.length,
      totalVolume: collections.reduce((sum, c) => sum + c.totalVolume, 0),
      totalMarketCap: collections.reduce((sum, c) => sum + c.marketCap, 0),
      averageFloorPrice: collections.reduce((sum, c) => sum + c.floorPrice, 0) / collections.length,
      totalHolders: collections.reduce((sum, c) => sum + c.holderCount, 0)
    };
  }, [filteredCollections]);

  const blockchainDistribution = useMemo(() => {
    const distribution = nftCollections.reduce((acc, collection) => {
      acc[collection.blockchain] = (acc[collection.blockchain] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return Object.entries(distribution).map(([blockchain, count]) => ({
      name: blockchain,
      value: count,
      percentage: (count / nftCollections.length) * 100
    }));
  }, [nftCollections]);

  const categoryDistribution = useMemo(() => {
    const distribution = nftCollections.reduce((acc, collection) => {
      acc[collection.category] = (acc[collection.category] || 0) + collection.totalVolume;
      return acc;
    }, {} as Record<string, number>);
    
    return Object.entries(distribution).map(([category, volume]) => ({
      category,
      volume,
      percentage: (volume / aggregatedMetrics.totalVolume) * 100
    }));
  }, [nftCollections, aggregatedMetrics.totalVolume]);

  const getInsightIcon = (category: string) => {
    switch (category) {
      case 'market_opportunity': return <Target className="h-4 w-4" />;
      case 'risk_assessment': return <Shield className="h-4 w-4" />;
      case 'collection_analysis': return <BarChart3 className="h-4 w-4" />;
      case 'trader_behavior': return <Users className="h-4 w-4" />;
      case 'technology_trend': return <Zap className="h-4 w-4" />;
      default: return <Brain className="h-4 w-4" />;
    }
  };

  const getTrendIcon = (category: string) => {
    switch (category) {
      case 'emerging_collection': return <Star className="h-4 w-4" />;
      case 'price_movement': return <TrendingUp className="h-4 w-4" />;
      case 'volume_spike': return <Activity className="h-4 w-4" />;
      case 'new_creator': return <Users className="h-4 w-4" />;
      case 'utility_adoption': return <Zap className="h-4 w-4" />;
      case 'cross_chain_activity': return <Network className="h-4 w-4" />;
      default: return <TrendingUp className="h-4 w-4" />;
    }
  };

  const getTraderTypeIcon = (type: string) => {
    switch (type) {
      case 'whale': return <Crown className="h-4 w-4" />;
      case 'collector': return <Gem className="h-4 w-4" />;
      case 'flipper': return <Zap className="h-4 w-4" />;
      case 'hodler': return <Shield className="h-4 w-4" />;
      case 'creator': return <Palette className="h-4 w-4" />;
      case 'institutional': return <Building2 className="h-4 w-4" />;
      case 'retail': return <Users className="h-4 w-4" />;
      default: return <Users className="h-4 w-4" />;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="w-full space-y-6 p-6 bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 min-h-screen">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <Gem className="h-8 w-8 text-purple-600" />
            NFT Marketplace Analytics
          </h1>
          <p className="text-gray-600 mt-1">Non-fungible token trading, collection performance, and market dynamics</p>
        </div>
        
        <div className="flex flex-wrap items-center gap-3">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search collections..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
            />
          </div>
          
          <select
            value={selectedBlockchain}
            onChange={(e) => setSelectedBlockchain(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="all">All Blockchains</option>
            <option value="Ethereum">Ethereum</option>
            <option value="Polygon">Polygon</option>
            <option value="Solana">Solana</option>
            <option value="Binance Smart Chain">BSC</option>
            <option value="Avalanche">Avalanche</option>
            <option value="Flow">Flow</option>
            <option value="Tezos">Tezos</option>
          </select>
          
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="all">All Categories</option>
            <option value="Art">Art</option>
            <option value="Gaming">Gaming</option>
            <option value="Collectibles">Collectibles</option>
            <option value="Photography">Photography</option>
            <option value="Music">Music</option>
            <option value="Video">Video</option>
            <option value="Utility">Utility</option>
            <option value="Virtual Real Estate">Virtual Real Estate</option>
            <option value="Sports">Sports</option>
          </select>
          
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm">Total Volume</p>
                <p className="text-2xl font-bold">${(aggregatedMetrics.totalVolume / 1000).toFixed(0)}K</p>
                <p className="text-purple-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +18.3% vs last period
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">Market Cap</p>
                <p className="text-2xl font-bold">${(aggregatedMetrics.totalMarketCap / 1000000).toFixed(0)}M</p>
                <p className="text-blue-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +12.7% vs last period
                </p>
              </div>
              <Gem className="h-8 w-8 text-blue-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm">Collections</p>
                <p className="text-2xl font-bold">{aggregatedMetrics.totalCollections.toLocaleString()}</p>
                <p className="text-green-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +8.9% vs last period
                </p>
              </div>
              <Box className="h-8 w-8 text-green-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm">Avg Floor Price</p>
                <p className="text-2xl font-bold">{aggregatedMetrics.averageFloorPrice.toFixed(2)} ETH</p>
                <p className="text-orange-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +5.4% vs last period
                </p>
              </div>
              <Activity className="h-8 w-8 text-orange-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-teal-500 to-teal-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-teal-100 text-sm">Total Holders</p>
                <p className="text-2xl font-bold">{(aggregatedMetrics.totalHolders / 1000).toFixed(0)}K</p>
                <p className="text-teal-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +14.1% vs last period
                </p>
              </div>
              <Users className="h-8 w-8 text-teal-200" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={viewMode} onValueChange={setViewMode} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="collections">Collections</TabsTrigger>
          <TabsTrigger value="traders">Traders</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="insights">AI Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Market Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  24-Hour Market Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ComposedChart data={marketplaceMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Area yAxisId="left" type="monotone" dataKey="totalVolume" stackId="1" stroke="#8B5CF6" fill="#C4B5FD" />
                    <Line yAxisId="right" type="monotone" dataKey="averageSalePrice" stroke="#10B981" strokeWidth={2} />
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Blockchain Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Network className="h-5 w-5" />
                  Blockchain Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Tooltip />
                    <Cell />
                  </RechartsPieChart>
                </ResponsiveContainer>
                <div className="grid grid-cols-2 gap-2 mt-4">
                  {blockchainDistribution.slice(0, 6).map((item, i) => (
                    <div key={i} className="flex items-center gap-2 text-sm">
                      <div className={`w-3 h-3 rounded-full bg-purple-${(i % 3 + 1) * 200}`}></div>
                      <span className="text-gray-600">{item.name}</span>
                      <span className="font-medium">{item.percentage.toFixed(1)}%</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Trading Activity */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Trading Activity & Price Movements
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <ComposedChart data={marketplaceMetrics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="timestamp" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Bar yAxisId="left" dataKey="totalTransactions" fill="#3B82F6" />
                  <Bar yAxisId="left" dataKey="salesCount" fill="#8B5CF6" />
                  <Line yAxisId="right" type="monotone" dataKey="floorPriceIndex" stroke="#F59E0B" strokeWidth={2} />
                </ComposedChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="collections" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredCollections.map((collection) => (
              <Card key={collection.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Box className="h-5 w-5" />
                      {collection.name}
                    </CardTitle>
                    <Badge variant="outline">{collection.blockchain}</Badge>
                  </div>
                  <p className="text-sm text-gray-600">{collection.category}</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Key Metrics */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-purple-50 p-3 rounded-lg text-center">
                      <p className="text-lg font-bold text-purple-600">{collection.floorPrice.toFixed(2)} ETH</p>
                      <p className="text-xs text-gray-600">Floor Price</p>
                    </div>
                    <div className="bg-blue-50 p-3 rounded-lg text-center">
                      <p className="text-lg font-bold text-blue-600">{(collection.totalVolume / 1000).toFixed(0)}K</p>
                      <p className="text-xs text-gray-600">Total Volume</p>
                    </div>
                    <div className="bg-green-50 p-3 rounded-lg text-center">
                      <p className="text-lg font-bold text-green-600">{collection.holderCount.toLocaleString()}</p>
                      <p className="text-xs text-gray-600">Holders</p>
                    </div>
                    <div className="bg-orange-50 p-3 rounded-lg text-center">
                      <p className="text-lg font-bold text-orange-600">{collection.mintedSupply.toLocaleString()}</p>
                      <p className="text-xs text-gray-600">Supply</p>
                    </div>
                  </div>

                  {/* Social Metrics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Community</h4>
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-600">Discord:</span>
                      <span className="font-medium">{(collection.socialMetrics.discordMembers / 1000).toFixed(0)}K</span>
                    </div>
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-600">Twitter:</span>
                      <span className="font-medium">{(collection.socialMetrics.twitterFollowers / 1000).toFixed(0)}K</span>
                    </div>
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-600">Sentiment:</span>
                      <span className="font-medium text-green-600">{collection.socialMetrics.socialSentiment.toFixed(1)}%</span>
                    </div>
                  </div>

                  {/* Utility Features */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Utility Features</h4>
                    <div className="flex flex-wrap gap-1">
                      {Object.entries(collection.utilityFeatures)
                        .filter(([, value]) => value)
                        .map(([feature, ]) => (
                          <Badge key={feature} variant="secondary" className="text-xs capitalize">
                            {feature.replace(/([A-Z])/g, ' $1').trim()}
                          </Badge>
                        ))}
                    </div>
                  </div>

                  {/* Performance Metrics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Performance</h4>
                    <div className="space-y-1">
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">Liquidity Score:</span>
                        <span className="font-medium">{collection.metrics.liquidityScore.toFixed(0)}%</span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">Velocity Score:</span>
                        <span className="font-medium">{collection.metrics.velocityScore.toFixed(0)}%</span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">Volatility:</span>
                        <span className="font-medium text-orange-600">{collection.metrics.volatilityIndex.toFixed(0)}%</span>
                      </div>
                    </div>
                  </div>

                  <Button variant="outline" className="w-full">
                    <Eye className="h-4 w-4 mr-2" />
                    View Collection Details
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="traders" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {traderProfiles.map((trader) => (
              <Card key={trader.address} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      {getTraderTypeIcon(trader.traderType)}
                      {trader.alias}
                    </CardTitle>
                    <Badge variant="outline" className="capitalize">
                      {trader.traderType}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 font-mono">{trader.address.slice(0, 10)}...{trader.address.slice(-8)}</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Portfolio Metrics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Portfolio Overview</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="bg-blue-50 p-2 rounded text-center">
                        <p className="font-bold text-blue-600">${(trader.portfolioValue / 1000).toFixed(0)}K</p>
                        <p className="text-xs text-gray-600">Portfolio Value</p>
                      </div>
                      <div className="bg-green-50 p-2 rounded text-center">
                        <p className="font-bold text-green-600">{trader.winRate.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Win Rate</p>
                      </div>
                      <div className="bg-purple-50 p-2 rounded text-center">
                        <p className="font-bold text-purple-600">{trader.totalTransactions}</p>
                        <p className="text-xs text-gray-600">Transactions</p>
                      </div>
                      <div className="bg-orange-50 p-2 rounded text-center">
                        <p className="font-bold text-orange-600">${(trader.totalVolume / 1000).toFixed(0)}K</p>
                        <p className="text-xs text-gray-600">Total Volume</p>
                      </div>
                    </div>
                  </div>

                  {/* P&L */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Profit & Loss</h4>
                    <div className={`text-center p-3 rounded-lg ${trader.profitLoss >= 0 ? 'bg-green-50' : 'bg-red-50'}`}>
                      <p className={`text-xl font-bold ${trader.profitLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {trader.profitLoss >= 0 ? '+' : ''}${(trader.profitLoss / 1000).toFixed(0)}K
                      </p>
                      <p className="text-xs text-gray-600">Total P&L</p>
                    </div>
                  </div>

                  {/* Trading Patterns */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Trading Profile</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Frequency:</span>
                        <span className="font-medium">{trader.tradingPatterns.tradingFrequency}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Avg Hold Time:</span>
                        <span className="font-medium">{Math.round(trader.averageHoldTime)}d</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Risk Tolerance:</span>
                        <span className="font-medium">{trader.tradingPatterns.riskTolerance.toFixed(0)}%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Decision Speed:</span>
                        <span className="font-medium capitalize">{trader.behaviorAnalysis.decisionMakingSpeed}</span>
                      </div>
                    </div>
                  </div>

                  {/* Collections */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Top Collections</h4>
                    <div className="space-y-1">
                      {trader.collections.slice(0, 3).map((collection, i) => (
                        <div key={i} className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Collection {collection.collectionId.split('-')[1]}</span>
                          <div className="text-right">
                            <p className="font-medium">{collection.tokensOwned} NFTs</p>
                            <p className={`text-xs ${collection.profitLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {collection.profitLoss >= 0 ? '+' : ''}${(collection.profitLoss / 1000).toFixed(0)}K
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Button variant="outline" size="sm" className="w-full">
                    <Eye className="h-4 w-4 mr-2" />
                    View Trader Profile
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {nftTrends.map((trend) => (
              <motion.div
                key={trend.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="group"
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        {getTrendIcon(trend.category)}
                        <Badge variant="outline" className="capitalize">
                          {trend.category.replace('_', ' ')}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getImpactColor(trend.impact)}>
                          {trend.impact}
                        </Badge>
                        <span className="text-sm text-gray-500">{trend.timeframe}</span>
                      </div>
                    </div>
                    <CardTitle className="text-lg leading-snug">{trend.title}</CardTitle>
                    <p className="text-sm text-gray-600 leading-relaxed">{trend.description}</p>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Trend Strength */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-600">Trend Strength</span>
                        <span className="font-medium">{trend.trendStrength.toFixed(0)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-purple-600 h-2 rounded-full" 
                          style={{ width: `${trend.trendStrength}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* Key Metrics */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Key Metrics</h4>
                      <div className="space-y-2">
                        {trend.keyMetrics.map((metric, i) => (
                          <div key={i} className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">{metric.metric}:</span>
                            <div className="text-right">
                              <span className="font-medium">{metric.value.toLocaleString()}</span>
                              <span className={`ml-1 text-xs ${metric.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                ({metric.change >= 0 ? '+' : ''}{metric.change.toFixed(1)}%)
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Drivers */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Key Drivers</h4>
                      <div className="flex flex-wrap gap-1">
                        {trend.drivers.map((driver, i) => (
                          <Badge key={i} variant="secondary" className="text-xs">{driver}</Badge>
                        ))}
                      </div>
                    </div>

                    {/* Actionable Insights */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Actionable Insights</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {trend.actionableInsights.slice(0, 2).map((insight, i) => (
                          <li key={i} className="flex items-start gap-2">
                            <span className="text-purple-600 mt-1">•</span>
                            <span>{insight}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t">
                      <span>Confidence: {trend.confidence.toFixed(0)}%</span>
                      <span>{new Date(trend.createdAt).toLocaleDateString()}</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredInsights.map((insight) => (
              <motion.div
                key={insight.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="group"
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        {getInsightIcon(insight.category)}
                        <Badge variant="outline" className="capitalize">
                          {insight.category.replace('_', ' ')}
                        </Badge>
                      </div>
                      <Badge className={getImpactColor(insight.impact)}>
                        {insight.impact}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg leading-snug">{insight.insight}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Data Points:</span>
                      <span className="font-medium">{insight.dataPoints.toLocaleString()}</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Confidence:</span>
                      <span className="font-medium">{insight.confidence.toFixed(1)}%</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Business Value:</span>
                      <span className="font-medium text-green-600">{insight.businessValue}</span>
                    </div>
                    
                    <div className="border-t pt-4">
                      <h4 className="font-medium text-gray-900 mb-2">Recommendation</h4>
                      <p className="text-sm text-gray-600 leading-relaxed">{insight.recommendation}</p>
                    </div>
                    
                    <div className="border-t pt-4">
                      <h4 className="font-medium text-gray-900 mb-2">Affected Segments</h4>
                      <div className="flex flex-wrap gap-1">
                        {insight.affectedSegments.map((segment, i) => (
                          <Badge key={i} variant="secondary" className="text-xs">{segment}</Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Clock className="h-3 w-3" />
                      <span>{insight.implementation.timeframe}</span>
                      <span>•</span>
                      <span className="capitalize">{insight.implementation.complexity} complexity</span>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Eye className="h-4 w-4 mr-2" />
                        Details
                      </Button>
                      <Button size="sm" className="flex-1">
                        <Rocket className="h-4 w-4 mr-2" />
                        Implement
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default NFTMarketplaceAnalytics;