/**
 * Spatial Commerce Insights Platform
 * Advanced analytics for location-aware commerce and spatial shopping behaviors
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MapPin,
  Navigation,
  Compass,
  Globe,
  Building2,
  Home,
  Store,
  Car,
  Plane,
  Users,
  ShoppingBag,
  TrendingUp,
  TrendingDown,
  Target,
  Eye,
  Activity,
  Clock,
  DollarSign,
  BarChart3,
  LineChart,
  PieChart,
  HeatMap,
  Layers,
  Zap,
  Brain,
  Settings,
  RefreshCw,
  Search,
  Filter,
  Download,
  Upload,
  Share,
  ExternalLink,
  Plus,
  Minus,
  Edit,
  Trash,
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  ArrowLeft,
  Star,
  Heart,
  ThumbsUp,
  MessageCircle,
  Flag,
  Award,
  Trophy,
  Crown,
  Gem,
  CreditCard,
  Wallet,
  Network,
  Wifi,
  Signal,
  Battery,
  Smartphone,
  Monitor,
  Tablet,
  Watch,
  Calendar,
  CheckCircle,
  AlertTriangle,
  Info,
  HelpCircle,
  BookOpen,
  FileText,
  File,
  Folder,
  Archive,
  Save,
  Mail,
  Phone,
  Bell,
  Lightbulb,
  Rocket,
  Sparkles,
  Wand2,
  Move3d,
  Crosshair,
  Focus,
  Gauge,
  Database,
  Server,
  Cloud,
  Cpu,
  Route,
  TreePine,
  Mountain,
  Waves,
  Sun,
  Moon,
  Coffee,
  Utensils,
  Shirt,
  Headphones,
  Laptop,
  Gamepad2
} from 'lucide-react';
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart as RechartsBarChart, Bar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ScatterChart, Scatter, ComposedChart, Cell, PieChart as RechartsPieChart, Pie } from 'recharts';

// TypeScript interfaces for spatial commerce
interface SpatialLocation {
  id: string;
  name: string;
  type: 'indoor' | 'outdoor' | 'mixed' | 'virtual';
  coordinates: {
    latitude: number;
    longitude: number;
    altitude?: number;
  };
  address: {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
  };
  characteristics: {
    size: number; // square meters
    capacity: number; // max concurrent users
    density: 'low' | 'medium' | 'high' | 'very_high';
    accessibility: string[];
    amenities: string[];
    demographics: string[];
  };
  businessContext: {
    category: string;
    subcategory: string;
    businessHours: string;
    seasonality: string;
    competitorDensity: number;
    footTraffic: number;
  };
  technicalInfrastructure: {
    wifiStrength: number;
    cellularCoverage: string;
    bluetoothBeacons: number;
    iotSensors: string[];
    digitalDisplays: number;
    arAnchors: number;
  };
  environmentalFactors: {
    lighting: 'excellent' | 'good' | 'fair' | 'poor' | 'variable';
    noise: number; // decibels
    temperature: number;
    humidity: number;
    airQuality: number;
    crowdFlow: string;
  };
}

interface SpatialShoppingSeason {
  userId: string;
  sessionId: string;
  location: SpatialLocation;
  startTime: string;
  endTime: string;
  duration: number;
  journey: {
    entryPoint: string;
    exitPoint: string;
    pathTaken: string[];
    stopsCount: number;
    backtracking: boolean;
    detours: number;
  };
  spatialBehavior: {
    movementPattern: 'linear' | 'exploratory' | 'focused' | 'social' | 'random';
    averageSpeed: number; // m/s
    standingTime: number; // seconds
    walkingTime: number; // seconds
    personalSpace: number; // meters
    proxemics: {
      intimate: number; // time spent within 0.5m of others
      personal: number; // time spent within 1.2m of others
      social: number; // time spent within 3.6m of others
      public: number; // time spent beyond 3.6m of others
    };
  };
  productInteractions: {
    itemsViewed: number;
    itemsHandled: number;
    comparisons: number;
    digitalEngagements: number;
    socialValidations: number;
    purchaseIntent: number;
    actualPurchases: number;
  };
  contextualInfluences: {
    companionPresence: boolean;
    groupSize: number;
    weatherImpact: number;
    timeOfDay: string;
    dayOfWeek: string;
    specialEvents: string[];
    promotions: string[];
  };
  locationSpecificMetrics: {
    queueWaitTime: number;
    serviceQuality: number;
    staffInteractions: number;
    facilityRating: number;
    safetyPerception: number;
    cleanliness: number;
    accessibility: number;
  };
  digitalIntegration: {
    appUsage: number; // minutes
    qrCodeScans: number;
    nfcTaps: number;
    beaconInteractions: number;
    arActivations: number;
    socialSharing: number;
    loyaltyPrograms: string[];
  };
  economicBehavior: {
    spendingAmount: number;
    paymentMethod: string;
    discountsUsed: string[];
    loyaltyPointsEarned: number;
    loyaltyPointsRedeemed: number;
    returnProbability: number;
    lifetimeValue: number;
  };
}

interface LocationAnalytics {
  locationId: string;
  locationName: string;
  timeframe: string;
  visitorMetrics: {
    totalVisitors: number;
    uniqueVisitors: number;
    returningVisitors: number;
    averageVisitDuration: number;
    peakHours: number[];
    seasonalTrends: { month: string; visitors: number }[];
  };
  spatialMetrics: {
    heatmapData: { x: number; y: number; intensity: number }[];
    popularZones: string[];
    bottleneckAreas: string[];
    flowPatterns: string[];
    utilizationRate: number;
    capacityOptimization: number;
  };
  commerceMetrics: {
    totalRevenue: number;
    averageOrderValue: number;
    conversionRate: number;
    basketSize: number;
    popularCategories: string[];
    crossSellSuccess: number;
    upsellSuccess: number;
  };
  experienceMetrics: {
    customerSatisfaction: number;
    npsScore: number;
    waitTimes: number;
    serviceQuality: number;
    facilityRating: number;
    recommendationRate: number;
  };
  competitiveAnalysis: {
    marketShare: number;
    competitorComparison: { name: string; score: number }[];
    uniqueSellingPoints: string[];
    improvementAreas: string[];
  };
}

interface SpatialInsight {
  id: string;
  category: 'location_optimization' | 'customer_flow' | 'product_placement' | 'experience_enhancement' | 'revenue_optimization';
  insight: string;
  location: string;
  dataPoints: number;
  confidence: number;
  impact: 'low' | 'medium' | 'high' | 'critical';
  recommendation: string;
  implementation: {
    complexity: 'simple' | 'moderate' | 'complex';
    cost: 'low' | 'medium' | 'high';
    timeframe: string;
    resources: string[];
    expectedROI: string;
  };
  affectedMetrics: string[];
  businessValue: string;
  priority: number;
  createdAt: string;
}

interface ZonePerformance {
  zoneId: string;
  zoneName: string;
  coordinates: { x: number; y: number; width: number; height: number };
  type: 'entrance' | 'product_display' | 'checkout' | 'service' | 'waiting' | 'transit' | 'social';
  metrics: {
    dwellTime: number;
    trafficCount: number;
    conversionRate: number;
    engagement: number;
    satisfaction: number;
    efficiency: number;
  };
  optimization: {
    currentLayout: string;
    suggestedChanges: string[];
    expectedImprovements: { metric: string; improvement: number }[];
  };
}

// Mock data generation functions
const generateSpatialLocations = (): SpatialLocation[] => {
  const locationTypes = ['indoor', 'outdoor', 'mixed', 'virtual'] as const;
  const categories = ['Retail Store', 'Shopping Mall', 'Pop-up Shop', 'Showroom', 'Market', 'Event Space'];
  
  return Array.from({ length: 12 }, (_, i) => ({
    id: `location-${i + 1}`,
    name: `${categories[i % categories.length]} ${i + 1}`,
    type: locationTypes[i % locationTypes.length],
    coordinates: {
      latitude: 40.7128 + (Math.random() - 0.5) * 0.1,
      longitude: -74.0060 + (Math.random() - 0.5) * 0.1,
      altitude: Math.random() * 100
    },
    address: {
      street: `${Math.floor(Math.random() * 9999) + 1} Commerce St`,
      city: 'New York',
      state: 'NY',
      country: 'USA',
      postalCode: `100${Math.floor(Math.random() * 100).toString().padStart(2, '0')}`
    },
    characteristics: {
      size: Math.floor(Math.random() * 5000) + 500,
      capacity: Math.floor(Math.random() * 500) + 50,
      density: ['low', 'medium', 'high', 'very_high'][Math.floor(Math.random() * 4)] as any,
      accessibility: ['Wheelchair Access', 'Elevator', 'Ramp', 'Audio Assistance'].slice(0, Math.floor(Math.random() * 3) + 1),
      amenities: ['WiFi', 'Parking', 'Restrooms', 'Food Court', 'ATM'].slice(0, Math.floor(Math.random() * 4) + 1),
      demographics: ['Young Adults', 'Families', 'Professionals', 'Seniors'].slice(0, Math.floor(Math.random() * 3) + 1)
    },
    businessContext: {
      category: categories[i % categories.length],
      subcategory: ['Fashion', 'Electronics', 'Food', 'Services'][Math.floor(Math.random() * 4)],
      businessHours: '9:00 AM - 9:00 PM',
      seasonality: ['Low', 'Medium', 'High'][Math.floor(Math.random() * 3)],
      competitorDensity: Math.random() * 10,
      footTraffic: Math.floor(Math.random() * 1000) + 100
    },
    technicalInfrastructure: {
      wifiStrength: Math.random() * 100,
      cellularCoverage: ['Excellent', 'Good', 'Fair', 'Poor'][Math.floor(Math.random() * 4)],
      bluetoothBeacons: Math.floor(Math.random() * 20),
      iotSensors: ['Temperature', 'Humidity', 'Motion', 'Sound', 'Air Quality'].slice(0, Math.floor(Math.random() * 4) + 1),
      digitalDisplays: Math.floor(Math.random() * 10),
      arAnchors: Math.floor(Math.random() * 15)
    },
    environmentalFactors: {
      lighting: ['excellent', 'good', 'fair', 'poor', 'variable'][Math.floor(Math.random() * 5)] as any,
      noise: Math.random() * 80 + 30,
      temperature: Math.random() * 10 + 20,
      humidity: Math.random() * 50 + 30,
      airQuality: Math.random() * 100,
      crowdFlow: ['Smooth', 'Congested', 'Variable', 'Peak-dependent'][Math.floor(Math.random() * 4)]
    }
  }));
};

const generateLocationAnalytics = (): LocationAnalytics[] => {
  return Array.from({ length: 8 }, (_, i) => ({
    locationId: `location-${i + 1}`,
    locationName: `Commerce Location ${i + 1}`,
    timeframe: 'Last 30 Days',
    visitorMetrics: {
      totalVisitors: Math.floor(Math.random() * 10000) + 2000,
      uniqueVisitors: Math.floor(Math.random() * 8000) + 1500,
      returningVisitors: Math.floor(Math.random() * 3000) + 500,
      averageVisitDuration: Math.random() * 120 + 30,
      peakHours: [10, 12, 14, 16, 18, 20].slice(0, Math.floor(Math.random() * 4) + 2),
      seasonalTrends: Array.from({ length: 12 }, (_, month) => ({
        month: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][month],
        visitors: Math.floor(Math.random() * 2000) + 500
      }))
    },
    spatialMetrics: {
      heatmapData: Array.from({ length: 50 }, () => ({
        x: Math.random() * 100,
        y: Math.random() * 100,
        intensity: Math.random() * 100
      })),
      popularZones: ['Main Entrance', 'Product Display', 'Checkout Area', 'Information Desk'].slice(0, Math.floor(Math.random() * 3) + 1),
      bottleneckAreas: ['Narrow Corridor', 'Single Checkout', 'Elevator Area'].slice(0, Math.floor(Math.random() * 2) + 1),
      flowPatterns: ['Clockwise', 'Direct Path', 'Exploratory', 'Social Clustering'].slice(0, Math.floor(Math.random() * 3) + 1),
      utilizationRate: Math.random() * 40 + 60,
      capacityOptimization: Math.random() * 30 + 70
    },
    commerceMetrics: {
      totalRevenue: Math.floor(Math.random() * 100000) + 20000,
      averageOrderValue: Math.random() * 200 + 50,
      conversionRate: Math.random() * 30 + 15,
      basketSize: Math.random() * 5 + 2,
      popularCategories: ['Fashion', 'Electronics', 'Home & Garden', 'Beauty', 'Sports'].slice(0, Math.floor(Math.random() * 4) + 1),
      crossSellSuccess: Math.random() * 40 + 20,
      upsellSuccess: Math.random() * 25 + 15
    },
    experienceMetrics: {
      customerSatisfaction: Math.random() * 20 + 80,
      npsScore: Math.random() * 60 + 40,
      waitTimes: Math.random() * 15 + 2,
      serviceQuality: Math.random() * 20 + 80,
      facilityRating: Math.random() * 20 + 80,
      recommendationRate: Math.random() * 30 + 70
    },
    competitiveAnalysis: {
      marketShare: Math.random() * 30 + 10,
      competitorComparison: [
        { name: 'Competitor A', score: Math.random() * 100 },
        { name: 'Competitor B', score: Math.random() * 100 },
        { name: 'Competitor C', score: Math.random() * 100 }
      ],
      uniqueSellingPoints: ['Prime Location', 'Excellent Service', 'Wide Selection', 'Competitive Pricing'].slice(0, Math.floor(Math.random() * 3) + 1),
      improvementAreas: ['Wait Times', 'Product Availability', 'Staff Training', 'Digital Integration'].slice(0, Math.floor(Math.random() * 2) + 1)
    }
  }));
};

const generateSpatialInsights = (): SpatialInsight[] => {
  const categories = ['location_optimization', 'customer_flow', 'product_placement', 'experience_enhancement', 'revenue_optimization'] as const;
  const locations = ['Main Store', 'Pop-up Location', 'Mall Kiosk', 'Flagship Store', 'Outlet Store'];
  
  return Array.from({ length: 15 }, (_, i) => ({
    id: `spatial-insight-${i + 1}`,
    category: categories[i % categories.length],
    insight: [
      'Customers spend 23% more time in well-lit areas, suggesting strategic lighting improvements',
      'High-traffic zones show 15% lower conversion, indicating flow optimization needed',
      'Product displays near entrances generate 34% more engagement than central locations',
      'Wait times above 3 minutes correlate with 28% higher abandonment rates',
      'Social zones drive 41% more cross-category purchases through group influence',
      'Mobile integration points increase dwell time by 18% and satisfaction by 22%',
      'Seasonal layout adjustments could boost revenue by 12% during peak periods',
      'Queue management improvements could reduce wait perception by 40%',
      'Strategic product placement could increase basket size by 15%',
      'Accessibility improvements could expand customer base by 8%',
      'Digital wayfinding reduces customer frustration by 35%',
      'Staff positioning optimization improves service quality scores by 20%',
      'Comfort amenities increase average visit duration by 25%',
      'Checkout flow improvements could reduce abandonment by 18%',
      'Environmental controls impact purchase decisions by 12%'
    ][i],
    location: locations[i % locations.length],
    dataPoints: Math.floor(Math.random() * 10000) + 1000,
    confidence: Math.random() * 30 + 70,
    impact: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
    recommendation: [
      'Install LED lighting system with zone-based controls',
      'Redesign store layout to distribute traffic more evenly',
      'Relocate high-conversion products to entrance areas',
      'Implement digital queue management system',
      'Create designated social interaction zones',
      'Deploy mobile-integrated display points',
      'Plan seasonal layout variations',
      'Add digital wait time displays and entertainment',
      'Use data-driven product adjacency optimization',
      'Install accessibility features and clear signage',
      'Deploy interactive wayfinding kiosks',
      'Optimize staff positioning based on traffic patterns',
      'Add seating areas and climate control zones',
      'Streamline checkout process with mobile options',
      'Implement smart environmental control systems'
    ][i],
    implementation: {
      complexity: ['simple', 'moderate', 'complex'][Math.floor(Math.random() * 3)] as any,
      cost: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
      timeframe: ['1-2 weeks', '1-2 months', '3-6 months', '6-12 months'][Math.floor(Math.random() * 4)],
      resources: ['Staff Training', 'Technology Investment', 'Layout Redesign', 'Infrastructure Upgrade'].slice(0, Math.floor(Math.random() * 3) + 1),
      expectedROI: `${Math.floor(Math.random() * 200) + 50}% within ${Math.floor(Math.random() * 12) + 3} months`
    },
    affectedMetrics: ['Conversion Rate', 'Dwell Time', 'Customer Satisfaction', 'Revenue', 'Traffic Flow'].slice(0, Math.floor(Math.random() * 4) + 1),
    businessValue: `$${Math.floor(Math.random() * 50000) + 10000} annual impact`,
    priority: Math.floor(Math.random() * 10) + 1,
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateZonePerformance = (): ZonePerformance[] => {
  const zoneTypes = ['entrance', 'product_display', 'checkout', 'service', 'waiting', 'transit', 'social'] as const;
  const zoneNames = ['Main Entrance', 'Featured Products', 'Express Checkout', 'Customer Service', 'Waiting Area', 'Central Corridor', 'Social Hub'];
  
  return Array.from({ length: 7 }, (_, i) => ({
    zoneId: `zone-${i + 1}`,
    zoneName: zoneNames[i],
    coordinates: {
      x: Math.random() * 100,
      y: Math.random() * 100,
      width: Math.random() * 30 + 10,
      height: Math.random() * 30 + 10
    },
    type: zoneTypes[i],
    metrics: {
      dwellTime: Math.random() * 300 + 60,
      trafficCount: Math.floor(Math.random() * 1000) + 100,
      conversionRate: Math.random() * 40 + 10,
      engagement: Math.random() * 100,
      satisfaction: Math.random() * 20 + 80,
      efficiency: Math.random() * 30 + 70
    },
    optimization: {
      currentLayout: 'Standard configuration with basic flow management',
      suggestedChanges: [
        'Optimize product placement based on traffic patterns',
        'Improve lighting and visibility in key areas',
        'Add digital wayfinding and information displays',
        'Reconfigure queue management for better flow',
        'Enhance staff positioning and service points',
        'Integrate mobile technology touchpoints',
        'Create clearer zone boundaries and navigation'
      ].slice(0, Math.floor(Math.random() * 4) + 2),
      expectedImprovements: [
        { metric: 'Dwell Time', improvement: Math.random() * 25 + 5 },
        { metric: 'Conversion Rate', improvement: Math.random() * 20 + 5 },
        { metric: 'Satisfaction', improvement: Math.random() * 15 + 5 },
        { metric: 'Efficiency', improvement: Math.random() * 30 + 10 }
      ]
    }
  }));
};

const SpatialCommerceInsights: React.FC = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('24h');
  const [selectedLocation, setSelectedLocation] = useState('all');
  const [selectedMetric, setSelectedMetric] = useState('traffic');
  const [viewMode, setViewMode] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');

  // Generated data
  const spatialLocations = useMemo(() => generateSpatialLocations(), []);
  const locationAnalytics = useMemo(() => generateLocationAnalytics(), []);
  const spatialInsights = useMemo(() => generateSpatialInsights(), []);
  const zonePerformance = useMemo(() => generateZonePerformance(), []);

  // Filtering and processing
  const filteredInsights = useMemo(() => {
    return spatialInsights.filter(insight => 
      insight.insight.toLowerCase().includes(searchTerm.toLowerCase()) ||
      insight.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      insight.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [spatialInsights, searchTerm]);

  const aggregatedMetrics = useMemo(() => {
    const analytics = selectedLocation === 'all' ? locationAnalytics : locationAnalytics.filter(a => a.locationId === selectedLocation);
    
    return {
      totalVisitors: analytics.reduce((sum, a) => sum + a.visitorMetrics.totalVisitors, 0),
      averageConversion: analytics.reduce((sum, a) => sum + a.commerceMetrics.conversionRate, 0) / analytics.length,
      totalRevenue: analytics.reduce((sum, a) => sum + a.commerceMetrics.totalRevenue, 0),
      averageSatisfaction: analytics.reduce((sum, a) => sum + a.experienceMetrics.customerSatisfaction, 0) / analytics.length,
      averageUtilization: analytics.reduce((sum, a) => sum + a.spatialMetrics.utilizationRate, 0) / analytics.length
    };
  }, [locationAnalytics, selectedLocation]);

  const trafficData = useMemo(() => {
    return Array.from({ length: 24 }, (_, i) => ({
      hour: `${i.toString().padStart(2, '0')}:00`,
      visitors: Math.floor(Math.random() * 200) + 50,
      conversion: Math.random() * 30 + 15,
      revenue: Math.floor(Math.random() * 5000) + 1000,
      satisfaction: Math.random() * 20 + 80
    }));
  }, []);

  const heatmapData = useMemo(() => {
    return Array.from({ length: 100 }, (_, i) => ({
      x: i % 10,
      y: Math.floor(i / 10),
      value: Math.random() * 100,
      visitors: Math.floor(Math.random() * 50) + 10
    }));
  }, []);

  const getInsightIcon = (category: string) => {
    switch (category) {
      case 'location_optimization': return <MapPin className="h-4 w-4" />;
      case 'customer_flow': return <Navigation className="h-4 w-4" />;
      case 'product_placement': return <Target className="h-4 w-4" />;
      case 'experience_enhancement': return <Star className="h-4 w-4" />;
      case 'revenue_optimization': return <DollarSign className="h-4 w-4" />;
      default: return <Lightbulb className="h-4 w-4" />;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="w-full space-y-6 p-6 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 min-h-screen">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <MapPin className="h-8 w-8 text-blue-600" />
            Spatial Commerce Insights
          </h1>
          <p className="text-gray-600 mt-1">Location-aware commerce analytics and spatial shopping behaviors</p>
        </div>
        
        <div className="flex flex-wrap items-center gap-3">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search insights..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
            />
          </div>
          
          <select
            value={selectedLocation}
            onChange={(e) => setSelectedLocation(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Locations</option>
            {spatialLocations.map(location => (
              <option key={location.id} value={location.id}>{location.name}</option>
            ))}
          </select>
          
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">Total Visitors</p>
                <p className="text-2xl font-bold">{aggregatedMetrics.totalVisitors.toLocaleString()}</p>
                <p className="text-blue-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +12.5% vs last period
                </p>
              </div>
              <Users className="h-8 w-8 text-blue-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm">Conversion Rate</p>
                <p className="text-2xl font-bold">{aggregatedMetrics.averageConversion.toFixed(1)}%</p>
                <p className="text-green-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +3.2% vs last period
                </p>
              </div>
              <Target className="h-8 w-8 text-green-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm">Total Revenue</p>
                <p className="text-2xl font-bold">${(aggregatedMetrics.totalRevenue / 1000).toFixed(0)}K</p>
                <p className="text-purple-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +8.7% vs last period
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm">Satisfaction</p>
                <p className="text-2xl font-bold">{aggregatedMetrics.averageSatisfaction.toFixed(1)}%</p>
                <p className="text-orange-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +2.1% vs last period
                </p>
              </div>
              <Star className="h-8 w-8 text-orange-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-teal-500 to-teal-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-teal-100 text-sm">Space Utilization</p>
                <p className="text-2xl font-bold">{aggregatedMetrics.averageUtilization.toFixed(1)}%</p>
                <p className="text-teal-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +5.4% vs last period
                </p>
              </div>
              <Building2 className="h-8 w-8 text-teal-200" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={viewMode} onValueChange={setViewMode} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="heatmap">Spatial Heatmap</TabsTrigger>
          <TabsTrigger value="zones">Zone Analysis</TabsTrigger>
          <TabsTrigger value="insights">AI Insights</TabsTrigger>
          <TabsTrigger value="locations">Location Details</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Traffic Patterns */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Hourly Traffic Patterns
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ComposedChart data={trafficData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="hour" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Bar yAxisId="left" dataKey="visitors" fill="#3B82F6" />
                    <Line yAxisId="right" type="monotone" dataKey="conversion" stroke="#10B981" strokeWidth={2} />
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Location Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Location Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsBarChart data={locationAnalytics.slice(0, 6)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="locationName" angle={-45} textAnchor="end" height={80} />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="commerceMetrics.conversionRate" fill="#8B5CF6" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Revenue Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="h-5 w-5" />
                Revenue and Satisfaction Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={trafficData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Area yAxisId="left" type="monotone" dataKey="revenue" stackId="1" stroke="#F59E0B" fill="#FEF3C7" />
                  <Line yAxisId="right" type="monotone" dataKey="satisfaction" stroke="#EF4444" strokeWidth={2} />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="heatmap" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Spatial Activity Heatmap
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">Visual representation of customer activity across physical space</p>
            </CardHeader>
            <CardContent>
              <div className="relative bg-gray-100 rounded-lg p-4 h-96">
                <div className="grid grid-cols-10 gap-1 h-full">
                  {heatmapData.map((cell, i) => (
                    <div
                      key={i}
                      className={`rounded-sm flex items-center justify-center text-xs font-medium transition-all hover:scale-105 cursor-pointer ${
                        cell.value > 80 ? 'bg-red-500 text-white' :
                        cell.value > 60 ? 'bg-orange-400 text-white' :
                        cell.value > 40 ? 'bg-yellow-400 text-gray-900' :
                        cell.value > 20 ? 'bg-green-400 text-gray-900' :
                        'bg-blue-200 text-gray-700'
                      }`}
                      title={`Position (${cell.x}, ${cell.y}): ${cell.visitors} visitors`}
                    >
                      {cell.visitors}
                    </div>
                  ))}
                </div>
                <div className="flex items-center justify-between mt-4 text-sm text-gray-600">
                  <span>Low Activity</span>
                  <div className="flex items-center gap-1">
                    <div className="w-4 h-4 bg-blue-200 rounded"></div>
                    <div className="w-4 h-4 bg-green-400 rounded"></div>
                    <div className="w-4 h-4 bg-yellow-400 rounded"></div>
                    <div className="w-4 h-4 bg-orange-400 rounded"></div>
                    <div className="w-4 h-4 bg-red-500 rounded"></div>
                  </div>
                  <span>High Activity</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="zones" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {zonePerformance.map((zone) => (
              <Card key={zone.zoneId} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Building2 className="h-5 w-5" />
                      {zone.zoneName}
                    </span>
                    <Badge variant="outline">{zone.type.replace('_', ' ')}</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-blue-600">{Math.round(zone.metrics.dwellTime)}s</p>
                      <p className="text-sm text-gray-600">Dwell Time</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-green-600">{zone.metrics.trafficCount}</p>
                      <p className="text-sm text-gray-600">Traffic Count</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-purple-600">{zone.metrics.conversionRate.toFixed(1)}%</p>
                      <p className="text-sm text-gray-600">Conversion</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-orange-600">{zone.metrics.satisfaction.toFixed(1)}%</p>
                      <p className="text-sm text-gray-600">Satisfaction</p>
                    </div>
                  </div>
                  
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-gray-900 mb-2">Optimization Potential</h4>
                    <div className="space-y-2">
                      {zone.optimization.expectedImprovements.slice(0, 2).map((improvement, i) => (
                        <div key={i} className="flex justify-between items-center text-sm">
                          <span className="text-gray-600">{improvement.metric}</span>
                          <span className="text-green-600 font-medium">+{improvement.improvement.toFixed(1)}%</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <Button variant="outline" size="sm" className="w-full">
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredInsights.map((insight) => (
              <motion.div
                key={insight.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="group"
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        {getInsightIcon(insight.category)}
                        <Badge variant="outline" className="capitalize">
                          {insight.category.replace('_', ' ')}
                        </Badge>
                      </div>
                      <Badge className={getImpactColor(insight.impact)}>
                        {insight.impact}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg leading-snug">{insight.insight}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Location:</span>
                      <span className="font-medium">{insight.location}</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Confidence:</span>
                      <span className="font-medium">{insight.confidence.toFixed(1)}%</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Business Value:</span>
                      <span className="font-medium text-green-600">{insight.businessValue}</span>
                    </div>
                    
                    <div className="border-t pt-4">
                      <h4 className="font-medium text-gray-900 mb-2">Recommendation</h4>
                      <p className="text-sm text-gray-600 leading-relaxed">{insight.recommendation}</p>
                    </div>
                    
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Clock className="h-3 w-3" />
                      <span>{insight.implementation.timeframe}</span>
                      <span>•</span>
                      <span className="capitalize">{insight.implementation.complexity} complexity</span>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Eye className="h-4 w-4 mr-2" />
                        Details
                      </Button>
                      <Button size="sm" className="flex-1">
                        <Rocket className="h-4 w-4 mr-2" />
                        Implement
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="locations" className="space-y-6">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            {locationAnalytics.map((analytics) => (
              <Card key={analytics.locationId} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Store className="h-5 w-5" />
                    {analytics.locationName}
                  </CardTitle>
                  <p className="text-sm text-gray-600">{analytics.timeframe}</p>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Visitor Metrics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Visitor Analytics</h4>
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <p className="text-xl font-bold text-blue-600">{analytics.visitorMetrics.totalVisitors.toLocaleString()}</p>
                        <p className="text-xs text-gray-600">Total Visitors</p>
                      </div>
                      <div>
                        <p className="text-xl font-bold text-green-600">{analytics.visitorMetrics.uniqueVisitors.toLocaleString()}</p>
                        <p className="text-xs text-gray-600">Unique Visitors</p>
                      </div>
                      <div>
                        <p className="text-xl font-bold text-purple-600">{Math.round(analytics.visitorMetrics.averageVisitDuration)}m</p>
                        <p className="text-xs text-gray-600">Avg Duration</p>
                      </div>
                    </div>
                  </div>

                  {/* Commerce Metrics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Commerce Performance</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-green-50 p-3 rounded-lg">
                        <p className="text-lg font-bold text-green-600">${(analytics.commerceMetrics.totalRevenue / 1000).toFixed(0)}K</p>
                        <p className="text-xs text-gray-600">Total Revenue</p>
                      </div>
                      <div className="bg-blue-50 p-3 rounded-lg">
                        <p className="text-lg font-bold text-blue-600">{analytics.commerceMetrics.conversionRate.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Conversion Rate</p>
                      </div>
                      <div className="bg-purple-50 p-3 rounded-lg">
                        <p className="text-lg font-bold text-purple-600">${analytics.commerceMetrics.averageOrderValue.toFixed(0)}</p>
                        <p className="text-xs text-gray-600">Avg Order Value</p>
                      </div>
                      <div className="bg-orange-50 p-3 rounded-lg">
                        <p className="text-lg font-bold text-orange-600">{analytics.commerceMetrics.basketSize.toFixed(1)}</p>
                        <p className="text-xs text-gray-600">Basket Size</p>
                      </div>
                    </div>
                  </div>

                  {/* Experience Metrics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Customer Experience</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Satisfaction</span>
                        <span className="font-medium">{analytics.experienceMetrics.customerSatisfaction.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">NPS Score</span>
                        <span className="font-medium">{analytics.experienceMetrics.npsScore.toFixed(0)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Wait Time</span>
                        <span className="font-medium">{analytics.experienceMetrics.waitTimes.toFixed(1)}min</span>
                      </div>
                    </div>
                  </div>

                  {/* Popular Categories */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Popular Categories</h4>
                    <div className="flex flex-wrap gap-2">
                      {analytics.commerceMetrics.popularCategories.map((category, i) => (
                        <Badge key={i} variant="secondary">{category}</Badge>
                      ))}
                    </div>
                  </div>

                  <Button variant="outline" className="w-full">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View Full Report
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SpatialCommerceInsights;