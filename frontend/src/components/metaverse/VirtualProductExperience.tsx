/**
 * Virtual Product Experience Analytics
 * Advanced analytics for immersive product interactions and virtual try-on experiences
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Eye,
  Hand,
  Zap,
  Users,
  ShoppingBag,
  TrendingUp,
  TrendingDown,
  RotateCw,
  Play,
  Pause,
  Volume2,
  Headphones,
  Gamepad2,
  MousePointer,
  Move3d,
  Maximize,
  Camera,
  Video,
  Image,
  Palette,
  Brush,
  Layers,
  Box,
  Cube,
  Sparkles,
  Wand2,
  Target,
  Crosshair,
  Navigation,
  Compass,
  MapPin,
  Clock,
  Timer,
  Activity,
  BarChart3,
  LineChart,
  PieChart,
  Gauge,
  Brain,
  Cpu,
  Settings,
  RefreshCw,
  Search,
  Filter,
  Download,
  Upload,
  Share,
  ExternalLink,
  Plus,
  Minus,
  Edit,
  Trash,
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  ArrowLeft,
  Star,
  Heart,
  ThumbsUp,
  ThumbsDown,
  MessageCircle,
  Flag,
  Award,
  Trophy,
  Crown,
  Gem,
  DollarSign,
  CreditCard,
  ShoppingCart,
  Wallet,
  Building2,
  Home,
  Store,
  Globe,
  Smartphone,
  Monitor,
  Tablet,
  Watch,
  Glasses,
  VrBox,
  Network,
  Wifi,
  Signal,
  Battery,
  Power,
  Link2,
  Unlock,
  Key,
  Shield,
  Lock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info,
  HelpCircle,
  BookOpen,
  FileText,
  File,
  Folder,
  Archive,
  Save,
  Mail,
  Phone,
  Bell,
  Calendar,
  Lightbulb,
  Rocket,
  Plane
} from 'lucide-react';
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart as RechartsBarChart, Bar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ScatterChart, Scatter, TreeMap, PieChart as RechartsPieChart, Pie } from 'recharts';

// TypeScript interfaces for virtual product experiences
interface VirtualProductSession {
  id: string;
  userId: string;
  userName: string;
  productId: string;
  productName: string;
  productCategory: string;
  sessionType: 'try_on' | '3d_view' | 'ar_placement' | 'customization' | 'size_fitting' | 'color_matching' | 'virtual_unboxing';
  platform: 'web_ar' | 'mobile_ar' | 'vr_headset' | 'smart_mirror' | 'holographic' | 'mixed_reality';
  device: string;
  startTime: string;
  endTime: string;
  duration: number;
  interactions: {
    rotations: number;
    zooms: number;
    colorChanges: number;
    sizeAdjustments: number;
    gestureCommands: number;
    voiceCommands: number;
    touchInteractions: number;
    eyeGazeTracking: number;
  };
  spatialData: {
    viewingAngles: { angle: number; duration: number }[];
    distanceFromProduct: number[];
    movementPattern: 'static' | 'circular' | 'linear' | 'random';
    roomScanAccuracy: number;
    placementAttempts: number;
  };
  biometrics: {
    engagementLevel: number;
    excitementScore: number;
    frustrationLevel: number;
    confidenceScore: number;
    attentionSpan: number;
  };
  customization: {
    optionsExplored: number;
    finalConfiguration: Record<string, string>;
    timeToDecision: number;
    changesBeforeDecision: number;
    satisfactionWithResult: number;
  };
  outcome: {
    completedExperience: boolean;
    addedToCart: boolean;
    purchaseIntent: number;
    shareAction: boolean;
    saveForLater: boolean;
    requestMoreInfo: boolean;
  };
  technicalMetrics: {
    renderQuality: 'low' | 'medium' | 'high' | 'ultra';
    loadTime: number;
    frameRate: number;
    trackingAccuracy: number;
    occlusionHandling: number;
    lightingRealism: number;
  };
  userFeedback: {
    experienceRating: number;
    realismRating: number;
    usabilityRating: number;
    helpfulnessRating: number;
    likelyToRecommend: number;
  };
  createdAt: string;
}

interface ProductExperienceMetric {
  timestamp: string;
  totalSessions: number;
  completionRate: number;
  averageDuration: number;
  conversionRate: number;
  engagementScore: number;
  tryOnSessions: number;
  arPlacementSessions: number;
  customizationSessions: number;
  userSatisfaction: number;
}

interface VirtualProduct {
  id: string;
  name: string;
  brand: string;
  category: string;
  subcategory: string;
  price: number;
  virtualFeatures: {
    has3DModel: boolean;
    hasARSupport: boolean;
    hasVRSupport: boolean;
    hasCustomization: boolean;
    hasTryOn: boolean;
    hasAnimation: boolean;
    hasSoundEffects: boolean;
    hasHapticFeedback: boolean;
  };
  technicalSpecs: {
    modelComplexity: 'low' | 'medium' | 'high' | 'ultra';
    textureResolution: string;
    animationFrames: number;
    fileSize: number;
    renderTime: number;
    supportedPlatforms: string[];
  };
  experienceMetrics: {
    totalViews: number;
    uniqueUsers: number;
    averageSessionTime: number;
    completionRate: number;
    conversionRate: number;
    shareRate: number;
    returnRate: number;
  };
  interactionHeatmap: {
    zone: string;
    interactions: number;
    dwellTime: number;
    conversionImpact: number;
  }[];
  userPreferences: {
    favoriteViewingAngle: number;
    mostUsedFeature: string;
    commonCustomizations: Record<string, number>;
    devicePreference: string;
  };
  performanceOptimization: {
    levelOfDetail: boolean;
    dynamicTextures: boolean;
    occlusionCulling: boolean;
    batchingEnabled: boolean;
    compressionRatio: number;
  };
  createdAt: string;
  lastUpdated: string;
}

interface ExperienceInsight {
  id: string;
  category: 'user_behavior' | 'technical_performance' | 'conversion_optimization' | 'platform_comparison' | 'feature_adoption';
  productId?: string;
  insight: string;
  recommendation: string;
  impact: 'low' | 'medium' | 'high' | 'critical';
  confidence: number;
  supportingData: {
    metric: string;
    value: number;
    trend: 'up' | 'down' | 'stable';
  }[];
  implementation: {
    effort: 'low' | 'medium' | 'high';
    timeframe: string;
    resources: string[];
    expectedROI: string;
  };
  affectedSessions: number;
  potentialImpact: string;
  createdAt: string;
}

interface SpatialInteraction {
  id: string;
  sessionId: string;
  interactionType: 'gaze' | 'touch' | 'gesture' | 'voice' | 'movement' | 'proximity';
  coordinates: { x: number; y: number; z: number };
  timestamp: string;
  duration: number;
  intensity: number;
  context: string;
  outcome: 'engagement' | 'conversion' | 'abandonment' | 'exploration';
}

interface FeatureUsageAnalytics {
  feature: string;
  usageCount: number;
  uniqueUsers: number;
  averageUsageTime: number;
  conversionRate: number;
  satisfactionScore: number;
  platformDistribution: Record<string, number>;
  demographicBreakdown: {
    ageGroup: string;
    usage: number;
    satisfaction: number;
  }[];
  technicalRequirements: {
    minDeviceSpecs: string;
    averagePerformance: number;
    errorRate: number;
  };
}

// Mock data generation functions
const generateVirtualProductSessions = (): VirtualProductSession[] => {
  const sessionTypes = ['try_on', '3d_view', 'ar_placement', 'customization', 'size_fitting', 'color_matching', 'virtual_unboxing'] as const;
  const platforms = ['web_ar', 'mobile_ar', 'vr_headset', 'smart_mirror', 'holographic', 'mixed_reality'] as const;
  const devices = ['iPhone 15 Pro', 'Galaxy S24', 'Quest 3', 'HoloLens 2', 'Magic Leap 2', 'iPad Pro', 'MacBook Pro', 'Smart Mirror v3'];
  const products = [
    'Wireless Headphones Pro', 'Designer Sunglasses', 'Gaming Chair Elite', 'Smart Watch Ultra',
    'Running Shoes Max', 'Luxury Handbag', 'Home Theater System', 'Kitchen Appliance Set',
    'Fashion Jacket', 'Jewelry Collection', 'Furniture Sofa', 'Art Sculpture'
  ];
  const categories = ['Electronics', 'Fashion', 'Furniture', 'Jewelry', 'Sports', 'Home', 'Art'];

  return Array.from({ length: 40 }, (_, i) => ({
    id: `vps-${i + 1}`,
    userId: `user-${Math.floor(Math.random() * 200) + 1}`,
    userName: `VirtualUser${i + 1}`,
    productId: `product-${(i % 12) + 1}`,
    productName: products[i % products.length],
    productCategory: categories[i % categories.length],
    sessionType: sessionTypes[i % sessionTypes.length],
    platform: platforms[i % platforms.length],
    device: devices[i % devices.length],
    startTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() - Math.random() * 6 * 24 * 60 * 60 * 1000).toISOString(),
    duration: Math.random() * 600 + 30, // 30 seconds to 10 minutes
    interactions: {
      rotations: Math.floor(Math.random() * 50) + 5,
      zooms: Math.floor(Math.random() * 20) + 2,
      colorChanges: Math.floor(Math.random() * 15),
      sizeAdjustments: Math.floor(Math.random() * 10),
      gestureCommands: Math.floor(Math.random() * 30),
      voiceCommands: Math.floor(Math.random() * 8),
      touchInteractions: Math.floor(Math.random() * 100) + 10,
      eyeGazeTracking: Math.floor(Math.random() * 200) + 50
    },
    spatialData: {
      viewingAngles: Array.from({ length: 8 }, (_, j) => ({
        angle: j * 45,
        duration: Math.random() * 30 + 5
      })),
      distanceFromProduct: Array.from({ length: 10 }, () => Math.random() * 3 + 0.5),
      movementPattern: ['static', 'circular', 'linear', 'random'][Math.floor(Math.random() * 4)] as any,
      roomScanAccuracy: Math.random() * 30 + 70,
      placementAttempts: Math.floor(Math.random() * 5) + 1
    },
    biometrics: {
      engagementLevel: Math.random() * 40 + 60,
      excitementScore: Math.random() * 50 + 50,
      frustrationLevel: Math.random() * 30 + 5,
      confidenceScore: Math.random() * 40 + 60,
      attentionSpan: Math.random() * 80 + 20
    },
    customization: {
      optionsExplored: Math.floor(Math.random() * 20) + 3,
      finalConfiguration: {
        color: ['Red', 'Blue', 'Black', 'White', 'Silver'][Math.floor(Math.random() * 5)],
        size: ['S', 'M', 'L', 'XL'][Math.floor(Math.random() * 4)],
        material: ['Leather', 'Fabric', 'Metal', 'Plastic'][Math.floor(Math.random() * 4)]
      },
      timeToDecision: Math.random() * 300 + 60,
      changesBeforeDecision: Math.floor(Math.random() * 10) + 1,
      satisfactionWithResult: Math.random() * 3 + 7
    },
    outcome: {
      completedExperience: Math.random() > 0.2,
      addedToCart: Math.random() > 0.6,
      purchaseIntent: Math.random() * 100,
      shareAction: Math.random() > 0.7,
      saveForLater: Math.random() > 0.5,
      requestMoreInfo: Math.random() > 0.8
    },
    technicalMetrics: {
      renderQuality: ['low', 'medium', 'high', 'ultra'][Math.floor(Math.random() * 4)] as any,
      loadTime: Math.random() * 5 + 1,
      frameRate: Math.random() * 30 + 60,
      trackingAccuracy: Math.random() * 20 + 80,
      occlusionHandling: Math.random() * 30 + 70,
      lightingRealism: Math.random() * 25 + 75
    },
    userFeedback: {
      experienceRating: Math.random() * 3 + 7,
      realismRating: Math.random() * 2.5 + 7.5,
      usabilityRating: Math.random() * 2 + 8,
      helpfulnessRating: Math.random() * 2.5 + 7.5,
      likelyToRecommend: Math.random() * 30 + 70
    },
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateProductExperienceMetrics = (): ProductExperienceMetric[] => {
  return Array.from({ length: 24 }, (_, i) => ({
    timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString().split('T')[1].split(':')[0] + ':00',
    totalSessions: Math.floor(Math.random() * 100) + 50,
    completionRate: Math.random() * 30 + 70,
    averageDuration: Math.random() * 200 + 120,
    conversionRate: Math.random() * 25 + 15,
    engagementScore: Math.random() * 30 + 70,
    tryOnSessions: Math.floor(Math.random() * 40) + 20,
    arPlacementSessions: Math.floor(Math.random() * 30) + 15,
    customizationSessions: Math.floor(Math.random() * 25) + 10,
    userSatisfaction: Math.random() * 20 + 80
  }));
};

const generateVirtualProducts = (): VirtualProduct[] => {
  const categories = ['Electronics', 'Fashion', 'Furniture', 'Jewelry', 'Sports', 'Home', 'Art'];
  const subcategories = {
    'Electronics': ['Headphones', 'Smartphones', 'Watches', 'Speakers'],
    'Fashion': ['Clothing', 'Shoes', 'Accessories', 'Bags'],
    'Furniture': ['Chairs', 'Tables', 'Sofas', 'Storage'],
    'Jewelry': ['Rings', 'Necklaces', 'Earrings', 'Bracelets'],
    'Sports': ['Equipment', 'Apparel', 'Footwear', 'Accessories'],
    'Home': ['Appliances', 'Decor', 'Lighting', 'Storage'],
    'Art': ['Sculptures', 'Paintings', 'Prints', 'Installations']
  };
  const brands = ['TechCorp', 'FashionHouse', 'ModernHome', 'LuxuryBrand', 'SportsPro', 'ArtStudio'];

  return Array.from({ length: 16 }, (_, i) => ({
    id: `product-${i + 1}`,
    name: [
      'Wireless Headphones Pro', 'Designer Sunglasses Elite', 'Gaming Chair Ultimate',
      'Smart Watch Series X', 'Running Shoes Max Performance', 'Luxury Handbag Collection',
      'Home Theater System 5.1', 'Kitchen Appliance Smart Set', 'Fashion Jacket Premium',
      'Jewelry Diamond Ring', 'Modern Sofa Design', 'Art Sculpture Contemporary',
      'Fitness Tracker Advanced', 'Bluetooth Speaker Portable', 'Dining Table Oak',
      'Wall Art Abstract'
    ][i],
    brand: brands[i % brands.length],
    category: categories[i % categories.length],
    subcategory: subcategories[categories[i % categories.length] as keyof typeof subcategories][Math.floor(Math.random() * 4)],
    price: Math.random() * 2000 + 100,
    virtualFeatures: {
      has3DModel: Math.random() > 0.1,
      hasARSupport: Math.random() > 0.2,
      hasVRSupport: Math.random() > 0.4,
      hasCustomization: Math.random() > 0.3,
      hasTryOn: Math.random() > 0.5,
      hasAnimation: Math.random() > 0.6,
      hasSoundEffects: Math.random() > 0.7,
      hasHapticFeedback: Math.random() > 0.8
    },
    technicalSpecs: {
      modelComplexity: ['low', 'medium', 'high', 'ultra'][Math.floor(Math.random() * 4)] as any,
      textureResolution: ['1024x1024', '2048x2048', '4096x4096'][Math.floor(Math.random() * 3)],
      animationFrames: Math.floor(Math.random() * 120) + 30,
      fileSize: Math.random() * 50 + 5, // MB
      renderTime: Math.random() * 2 + 0.5, // seconds
      supportedPlatforms: ['iOS', 'Android', 'Web', 'VR', 'AR'].slice(0, Math.floor(Math.random() * 3) + 2)
    },
    experienceMetrics: {
      totalViews: Math.floor(Math.random() * 10000) + 1000,
      uniqueUsers: Math.floor(Math.random() * 5000) + 500,
      averageSessionTime: Math.random() * 300 + 120,
      completionRate: Math.random() * 30 + 70,
      conversionRate: Math.random() * 20 + 10,
      shareRate: Math.random() * 15 + 5,
      returnRate: Math.random() * 25 + 15
    },
    interactionHeatmap: [
      { zone: 'Front View', interactions: Math.floor(Math.random() * 500) + 100, dwellTime: Math.random() * 60 + 30, conversionImpact: Math.random() * 20 + 10 },
      { zone: 'Side View', interactions: Math.floor(Math.random() * 300) + 50, dwellTime: Math.random() * 40 + 20, conversionImpact: Math.random() * 15 + 5 },
      { zone: 'Back View', interactions: Math.floor(Math.random() * 200) + 30, dwellTime: Math.random() * 30 + 15, conversionImpact: Math.random() * 10 + 3 },
      { zone: 'Detail Close-up', interactions: Math.floor(Math.random() * 400) + 80, dwellTime: Math.random() * 50 + 25, conversionImpact: Math.random() * 25 + 15 },
      { zone: 'Customization', interactions: Math.floor(Math.random() * 250) + 40, dwellTime: Math.random() * 80 + 40, conversionImpact: Math.random() * 30 + 20 }
    ],
    userPreferences: {
      favoriteViewingAngle: Math.floor(Math.random() * 360),
      mostUsedFeature: ['3D Rotation', 'Color Change', 'Size Adjustment', 'AR Placement', 'Zoom'][Math.floor(Math.random() * 5)],
      commonCustomizations: {
        'Color': Math.random() * 100,
        'Size': Math.random() * 80,
        'Material': Math.random() * 60,
        'Style': Math.random() * 40
      },
      devicePreference: ['Mobile', 'Desktop', 'VR Headset', 'AR Glasses'][Math.floor(Math.random() * 4)]
    },
    performanceOptimization: {
      levelOfDetail: Math.random() > 0.3,
      dynamicTextures: Math.random() > 0.4,
      occlusionCulling: Math.random() > 0.2,
      batchingEnabled: Math.random() > 0.5,
      compressionRatio: Math.random() * 0.4 + 0.6
    },
    createdAt: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
    lastUpdated: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateExperienceInsights = (): ExperienceInsight[] => {
  const categories = ['user_behavior', 'technical_performance', 'conversion_optimization', 'platform_comparison', 'feature_adoption'] as const;
  const impacts = ['low', 'medium', 'high', 'critical'] as const;

  return Array.from({ length: 12 }, (_, i) => ({
    id: `insight-${i + 1}`,
    category: categories[i % categories.length],
    productId: Math.random() > 0.5 ? `product-${Math.floor(Math.random() * 16) + 1}` : undefined,
    insight: [
      'AR placement sessions have 45% higher conversion rates than 3D viewing',
      'Users spend 300% more time in customization mode on VR platforms',
      'Mobile AR sessions show 60% higher completion rates on newer devices',
      'Voice commands increase user engagement by 80% in VR environments',
      'Eye tracking data reveals users focus 70% more on product details',
      'Haptic feedback increases purchase confidence by 35%',
      'Social sharing from AR sessions drives 200% more organic traffic',
      'Multi-angle viewing increases conversion probability by 25%',
      'Virtual try-on reduces return rates by 40%',
      'Real-time lighting adaptation improves realism scores by 30%',
      'Gesture-based interactions have 50% higher satisfaction ratings',
      'Cross-platform compatibility increases user retention by 65%'
    ][i],
    recommendation: [
      'Prioritize AR placement features for high-value products',
      'Expand customization options for VR-compatible products',
      'Optimize AR experiences for latest mobile hardware',
      'Implement voice command support across all platforms',
      'Add detailed zoom and inspection features to product views',
      'Integrate haptic feedback for compatible devices',
      'Add one-click sharing from AR experiences to social platforms',
      'Implement automatic rotation and guided viewing tours',
      'Expand virtual try-on to more product categories',
      'Implement dynamic lighting based on user environment',
      'Replace traditional UI controls with gesture-based alternatives',
      'Ensure consistent experience quality across all platforms'
    ][i],
    impact: impacts[i % impacts.length],
    confidence: Math.random() * 25 + 75,
    supportingData: [
      { metric: 'Conversion Rate', value: Math.random() * 50 + 25, trend: 'up' as const },
      { metric: 'Session Duration', value: Math.random() * 200 + 100, trend: 'up' as const },
      { metric: 'User Satisfaction', value: Math.random() * 20 + 80, trend: 'up' as const }
    ],
    implementation: {
      effort: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
      timeframe: ['2-4 weeks', '1-2 months', '2-3 months', '3-6 months'][Math.floor(Math.random() * 4)],
      resources: ['Development Team', 'UX/UI Designers', '3D Artists', 'QA Team'].slice(0, Math.floor(Math.random() * 3) + 1),
      expectedROI: `${Math.floor(Math.random() * 200 + 100)}% increase in conversions`
    },
    affectedSessions: Math.floor(Math.random() * 1000) + 100,
    potentialImpact: [
      '$50k monthly revenue increase',
      '25% reduction in cart abandonment',
      '40% improvement in user engagement',
      '30% increase in customer satisfaction',
      '35% boost in product discovery',
      '45% growth in social sharing',
      '50% reduction in support tickets',
      '60% improvement in brand perception'
    ][i % 8],
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateSpatialInteractions = (): SpatialInteraction[] => {
  const interactionTypes = ['gaze', 'touch', 'gesture', 'voice', 'movement', 'proximity'] as const;
  const outcomes = ['engagement', 'conversion', 'abandonment', 'exploration'] as const;

  return Array.from({ length: 100 }, (_, i) => ({
    id: `spatial-${i + 1}`,
    sessionId: `vps-${Math.floor(Math.random() * 40) + 1}`,
    interactionType: interactionTypes[i % interactionTypes.length],
    coordinates: {
      x: Math.random() * 10 - 5,
      y: Math.random() * 5,
      z: Math.random() * 10 - 5
    },
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
    duration: Math.random() * 30 + 1,
    intensity: Math.random() * 100,
    context: [
      'Product rotation', 'Color selection', 'Size adjustment', 'Zoom interaction',
      'AR placement', 'Customization menu', 'Detail inspection', 'Social sharing'
    ][i % 8],
    outcome: outcomes[i % outcomes.length]
  }));
};

const generateFeatureUsageAnalytics = (): FeatureUsageAnalytics[] => {
  const features = [
    '3D Rotation', 'Color Customization', 'Size Adjustment', 'AR Placement',
    'Virtual Try-On', 'Zoom & Inspect', 'Animation Playback', 'Social Sharing',
    'Voice Commands', 'Gesture Controls', 'Eye Tracking', 'Haptic Feedback'
  ];

  return features.map((feature, i) => ({
    feature,
    usageCount: Math.floor(Math.random() * 5000) + 1000,
    uniqueUsers: Math.floor(Math.random() * 2000) + 500,
    averageUsageTime: Math.random() * 120 + 30,
    conversionRate: Math.random() * 30 + 15,
    satisfactionScore: Math.random() * 2 + 8,
    platformDistribution: {
      'Mobile AR': Math.random() * 50 + 25,
      'Web AR': Math.random() * 30 + 15,
      'VR Headset': Math.random() * 25 + 10,
      'Smart Mirror': Math.random() * 15 + 5
    },
    demographicBreakdown: [
      { ageGroup: '18-24', usage: Math.random() * 30 + 20, satisfaction: Math.random() * 2 + 8 },
      { ageGroup: '25-34', usage: Math.random() * 35 + 25, satisfaction: Math.random() * 2 + 8 },
      { ageGroup: '35-44', usage: Math.random() * 25 + 15, satisfaction: Math.random() * 2 + 7.5 },
      { ageGroup: '45+', usage: Math.random() * 20 + 10, satisfaction: Math.random() * 2 + 7 }
    ],
    technicalRequirements: {
      minDeviceSpecs: ['Entry Level', 'Mid Range', 'High End', 'Flagship'][Math.floor(Math.random() * 4)],
      averagePerformance: Math.random() * 30 + 70,
      errorRate: Math.random() * 5 + 1
    }
  }));
};

const VirtualProductExperience: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [platformFilter, setPlatformFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [activeTab, setActiveTab] = useState('overview');

  // Generate mock data
  const productSessions = useMemo(() => generateVirtualProductSessions(), []);
  const experienceMetrics = useMemo(() => generateProductExperienceMetrics(), []);
  const virtualProducts = useMemo(() => generateVirtualProducts(), []);
  const experienceInsights = useMemo(() => generateExperienceInsights(), []);
  const spatialInteractions = useMemo(() => generateSpatialInteractions(), []);
  const featureUsage = useMemo(() => generateFeatureUsageAnalytics(), []);

  // Filter and search logic
  const filteredSessions = useMemo(() => {
    return productSessions.filter(session => {
      const matchesSearch = session.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          session.productName.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesPlatform = platformFilter === 'all' || session.platform === platformFilter;
      const matchesCategory = categoryFilter === 'all' || session.productCategory === categoryFilter;
      return matchesSearch && matchesPlatform && matchesCategory;
    });
  }, [productSessions, searchTerm, platformFilter, categoryFilter]);

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'web_ar': return <Monitor className="h-4 w-4" />;
      case 'mobile_ar': return <Smartphone className="h-4 w-4" />;
      case 'vr_headset': return <Glasses className="h-4 w-4" />;
      case 'smart_mirror': return <Eye className="h-4 w-4" />;
      case 'holographic': return <Sparkles className="h-4 w-4" />;
      case 'mixed_reality': return <Layers className="h-4 w-4" />;
      default: return <Monitor className="h-4 w-4" />;
    }
  };

  const getSessionTypeColor = (type: string) => {
    switch (type) {
      case 'try_on': return 'bg-purple-500';
      case '3d_view': return 'bg-blue-500';
      case 'ar_placement': return 'bg-green-500';
      case 'customization': return 'bg-orange-500';
      case 'size_fitting': return 'bg-pink-500';
      case 'color_matching': return 'bg-cyan-500';
      case 'virtual_unboxing': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'ultra': return 'text-green-600';
      case 'high': return 'text-blue-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="p-6 space-y-6 bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 min-h-screen">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
            Virtual Product Experience Analytics
          </h1>
          <p className="text-gray-600 mt-2">Advanced analytics for immersive product interactions and virtual try-on experiences</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Refresh Data
          </Button>
          <Button className="flex items-center gap-2 bg-gradient-to-r from-indigo-500 to-purple-500">
            <Box className="h-4 w-4" />
            Virtual Experiences
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sessions">Experience Sessions</TabsTrigger>
          <TabsTrigger value="products">Virtual Products</TabsTrigger>
          <TabsTrigger value="features">Feature Usage</TabsTrigger>
          <TabsTrigger value="spatial">Spatial Analytics</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="bg-gradient-to-br from-indigo-500 to-indigo-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-indigo-100 text-sm font-medium">Virtual Sessions</p>
                    <p className="text-3xl font-bold">{productSessions.length}</p>
                  </div>
                  <Eye className="h-8 w-8 text-indigo-200" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="bg-indigo-400 text-indigo-900">
                    +{Math.floor(Math.random() * 25 + 15)}% vs last week
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm font-medium">Avg Experience Time</p>
                    <p className="text-3xl font-bold">
                      {formatDuration(productSessions.reduce((sum, s) => sum + s.duration, 0) / productSessions.length)}
                    </p>
                  </div>
                  <Timer className="h-8 w-8 text-purple-200" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="bg-purple-400 text-purple-900">
                    Immersive Engagement
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-pink-500 to-pink-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-pink-100 text-sm font-medium">Conversion Rate</p>
                    <p className="text-3xl font-bold">
                      {Math.floor(productSessions.filter(s => s.outcome.addedToCart).length / productSessions.length * 100)}%
                    </p>
                  </div>
                  <ShoppingCart className="h-8 w-8 text-pink-200" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="bg-pink-400 text-pink-900">
                    Virtual Commerce
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-cyan-500 to-cyan-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-cyan-100 text-sm font-medium">Satisfaction Score</p>
                    <p className="text-3xl font-bold">
                      {(productSessions.reduce((sum, s) => sum + s.userFeedback.experienceRating, 0) / productSessions.length).toFixed(1)}
                    </p>
                  </div>
                  <Star className="h-8 w-8 text-cyan-200" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="bg-cyan-400 text-cyan-900">
                    /10 Rating
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Experience Engagement Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={experienceMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="totalSessions" stroke="#6366f1" fill="#6366f1" fillOpacity={0.6} />
                    <Area type="monotone" dataKey="engagementScore" stroke="#8b5cf6" fill="#8b5cf6" fillOpacity={0.4} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Conversion & Satisfaction
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsLineChart data={experienceMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="conversionRate" stroke="#10b981" strokeWidth={2} />
                    <Line type="monotone" dataKey="completionRate" stroke="#f59e0b" strokeWidth={2} />
                    <Line type="monotone" dataKey="userSatisfaction" stroke="#ec4899" strokeWidth={2} />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Box className="h-5 w-5" />
                  Session Type Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsBarChart data={experienceMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="tryOnSessions" fill="#8b5cf6" />
                    <Bar dataKey="arPlacementSessions" fill="#06b6d4" />
                    <Bar dataKey="customizationSessions" fill="#f59e0b" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gauge className="h-5 w-5" />
                  Platform Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Array.from(new Set(productSessions.map(s => s.platform))).map((platform) => {
                    const platformSessions = productSessions.filter(s => s.platform === platform);
                    const avgSatisfaction = platformSessions.reduce((sum, s) => sum + s.userFeedback.experienceRating, 0) / platformSessions.length;
                    const conversionRate = platformSessions.filter(s => s.outcome.addedToCart).length / platformSessions.length * 100;
                    
                    return (
                      <div key={platform} className="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg">
                        <div className="flex items-center gap-3">
                          {getPlatformIcon(platform)}
                          <div>
                            <p className="font-medium capitalize">{platform.replace('_', ' ')}</p>
                            <p className="text-sm text-gray-600">{platformSessions.length} sessions</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-green-600">{conversionRate.toFixed(1)}%</p>
                          <p className="text-sm text-gray-600">{avgSatisfaction.toFixed(1)}/10</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="sessions" className="space-y-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search experience sessions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={platformFilter}
                onChange={(e) => setPlatformFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Platforms</option>
                <option value="web_ar">Web AR</option>
                <option value="mobile_ar">Mobile AR</option>
                <option value="vr_headset">VR Headset</option>
                <option value="smart_mirror">Smart Mirror</option>
                <option value="holographic">Holographic</option>
                <option value="mixed_reality">Mixed Reality</option>
              </select>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Categories</option>
                <option value="Electronics">Electronics</option>
                <option value="Fashion">Fashion</option>
                <option value="Furniture">Furniture</option>
                <option value="Jewelry">Jewelry</option>
                <option value="Sports">Sports</option>
                <option value="Home">Home</option>
                <option value="Art">Art</option>
              </select>
            </div>
          </div>

          <div className="grid gap-4">
            <AnimatePresence>
              {filteredSessions.map((session) => (
                <motion.div
                  key={session.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card className="hover:shadow-lg transition-shadow bg-gradient-to-r from-white to-gray-50">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <div className="w-10 h-10 bg-gradient-to-br from-indigo-400 to-purple-400 rounded-full flex items-center justify-center text-white font-bold">
                              {session.userName.slice(0, 2).toUpperCase()}
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold">{session.userName}</h3>
                              <p className="text-sm text-gray-600">{session.productName} • {session.device}</p>
                            </div>
                            <div className={`w-3 h-3 rounded-full ${getSessionTypeColor(session.sessionType)}`} />
                            <Badge variant="outline" className="flex items-center gap-1">
                              {getPlatformIcon(session.platform)}
                              {session.platform.replace('_', ' ')}
                            </Badge>
                            <Badge variant="secondary">{formatDuration(session.duration)}</Badge>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 text-sm">
                            <div>
                              <p className="text-gray-600">Interactions</p>
                              <p className="font-semibold">{session.interactions.touchInteractions + session.interactions.gestureCommands}</p>
                              <p className="text-xs text-gray-500">
                                {session.interactions.rotations} rotations, {session.interactions.zooms} zooms
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-600">Customization</p>
                              <p className="font-semibold">{session.customization.optionsExplored} options</p>
                              <p className="text-xs text-gray-500">
                                {session.customization.changesBeforeDecision} changes made
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-600">Engagement</p>
                              <p className="font-semibold">{Math.floor(session.biometrics.engagementLevel)}%</p>
                              <p className="text-xs text-gray-500">
                                {Math.floor(session.biometrics.excitementScore)}% excitement
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-600">Outcome</p>
                              <p className={`font-semibold ${session.outcome.addedToCart ? 'text-green-600' : 'text-gray-600'}`}>
                                {session.outcome.addedToCart ? 'Converted' : 'Browsed'}
                              </p>
                              <p className="text-xs text-gray-500">
                                {Math.floor(session.outcome.purchaseIntent)}% intent
                              </p>
                            </div>
                          </div>

                          <div className="bg-gradient-to-r from-indigo-50 to-purple-50 p-4 rounded-lg mb-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                              <div>
                                <p className="font-medium text-indigo-700">Technical Performance</p>
                                <p className={`${getQualityColor(session.technicalMetrics.renderQuality)} capitalize`}>
                                  {session.technicalMetrics.renderQuality} Quality
                                </p>
                                <p className="text-xs text-gray-600">
                                  {Math.floor(session.technicalMetrics.frameRate)} FPS • {session.technicalMetrics.loadTime.toFixed(1)}s load
                                </p>
                              </div>
                              <div>
                                <p className="font-medium text-purple-700">User Feedback</p>
                                <p className="text-yellow-600">
                                  {session.userFeedback.experienceRating.toFixed(1)}/10 ⭐
                                </p>
                                <p className="text-xs text-gray-600">
                                  {session.userFeedback.realismRating.toFixed(1)}/10 realism
                                </p>
                              </div>
                              <div>
                                <p className="font-medium text-pink-700">Spatial Behavior</p>
                                <p className="capitalize">{session.spatialData.movementPattern.replace('_', ' ')}</p>
                                <p className="text-xs text-gray-600">
                                  {session.spatialData.placementAttempts} placement attempts
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="flex flex-wrap gap-2">
                            {session.outcome.completedExperience && (
                              <Badge variant="outline" className="border-green-500 text-green-700">
                                Completed
                              </Badge>
                            )}
                            {session.outcome.shareAction && (
                              <Badge variant="outline" className="border-blue-500 text-blue-700">
                                Shared
                              </Badge>
                            )}
                            {session.outcome.saveForLater && (
                              <Badge variant="outline" className="border-purple-500 text-purple-700">
                                Saved
                              </Badge>
                            )}
                          </div>
                        </div>

                        <div className="flex flex-col gap-2">
                          <Button size="sm" variant="outline" className="flex items-center gap-1">
                            <Play className="h-4 w-4" />
                            Replay
                          </Button>
                          <Button size="sm" variant="outline" className="flex items-center gap-1">
                            <BarChart3 className="h-4 w-4" />
                            Analytics
                          </Button>
                          <Button size="sm" variant="outline">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </TabsContent>

        <TabsContent value="products" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Virtual Products</h2>
            <Button className="flex items-center gap-2 bg-gradient-to-r from-indigo-500 to-purple-500">
              <Plus className="h-4 w-4" />
              Add Product
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {virtualProducts.map((product) => (
              <Card key={product.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{product.name}</CardTitle>
                    <Badge variant="outline">${Math.floor(product.price)}</Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{product.brand}</Badge>
                    <Badge variant="outline" className="capitalize">{product.category}</Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Total Views</p>
                      <p className="font-semibold">{product.experienceMetrics.totalViews.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Unique Users</p>
                      <p className="font-semibold">{product.experienceMetrics.uniqueUsers.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Conversion Rate</p>
                      <p className="font-semibold text-green-600">{product.experienceMetrics.conversionRate.toFixed(1)}%</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Avg Session</p>
                      <p className="font-semibold">{formatDuration(product.experienceMetrics.averageSessionTime)}</p>
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">Virtual Features:</p>
                    <div className="grid grid-cols-2 gap-1 text-xs">
                      {product.virtualFeatures.has3DModel && (
                        <span className="flex items-center gap-1 text-green-600">
                          <CheckCircle className="h-3 w-3" /> 3D Model
                        </span>
                      )}
                      {product.virtualFeatures.hasARSupport && (
                        <span className="flex items-center gap-1 text-blue-600">
                          <CheckCircle className="h-3 w-3" /> AR Support
                        </span>
                      )}
                      {product.virtualFeatures.hasVRSupport && (
                        <span className="flex items-center gap-1 text-purple-600">
                          <CheckCircle className="h-3 w-3" /> VR Support
                        </span>
                      )}
                      {product.virtualFeatures.hasCustomization && (
                        <span className="flex items-center gap-1 text-orange-600">
                          <CheckCircle className="h-3 w-3" /> Customization
                        </span>
                      )}
                      {product.virtualFeatures.hasTryOn && (
                        <span className="flex items-center gap-1 text-pink-600">
                          <CheckCircle className="h-3 w-3" /> Try-On
                        </span>
                      )}
                      {product.virtualFeatures.hasAnimation && (
                        <span className="flex items-center gap-1 text-cyan-600">
                          <CheckCircle className="h-3 w-3" /> Animation
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-3 rounded-lg">
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div>
                        <p className="text-gray-600">Model Quality</p>
                        <p className={`font-medium capitalize ${getQualityColor(product.technicalSpecs.modelComplexity)}`}>
                          {product.technicalSpecs.modelComplexity}
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-600">File Size</p>
                        <p className="font-medium">{product.technicalSpecs.fileSize.toFixed(1)} MB</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Platforms</p>
                        <p className="font-medium">{product.technicalSpecs.supportedPlatforms.length}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Render Time</p>
                        <p className="font-medium">{product.technicalSpecs.renderTime.toFixed(1)}s</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">Top Interaction Zones:</p>
                    <div className="space-y-1">
                      {product.interactionHeatmap.slice(0, 3).map((zone, idx) => (
                        <div key={idx} className="flex justify-between text-xs">
                          <span>{zone.zone}</span>
                          <span className="font-medium text-green-600">
                            +{zone.conversionImpact.toFixed(1)}%
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1 flex items-center gap-1">
                      <Eye className="h-4 w-4" />
                      View
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1 flex items-center gap-1">
                      <BarChart3 className="h-4 w-4" />
                      Analytics
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="features" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Feature Usage Analytics</h2>
            <Button variant="outline" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Filter Features
            </Button>
          </div>

          <div className="grid gap-6">
            {featureUsage.map((feature) => (
              <Card key={feature.feature} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    {feature.feature}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div className="bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-blue-700 mb-2">Usage Statistics</h4>
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <span>Total Usage:</span>
                          <span className="font-medium">{feature.usageCount.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Unique Users:</span>
                          <span className="font-medium">{feature.uniqueUsers.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Avg Time:</span>
                          <span className="font-medium">{formatDuration(feature.averageUsageTime)}</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-green-700 mb-2">Performance</h4>
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <span>Conversion Rate:</span>
                          <span className="font-medium text-green-600">{feature.conversionRate.toFixed(1)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Satisfaction:</span>
                          <span className="font-medium">{feature.satisfactionScore.toFixed(1)}/10</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Error Rate:</span>
                          <span className="font-medium">{feature.technicalRequirements.errorRate.toFixed(1)}%</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-purple-700 mb-2">Platform Distribution</h4>
                      <div className="space-y-1">
                        {Object.entries(feature.platformDistribution).map(([platform, percentage]) => (
                          <div key={platform} className="flex justify-between">
                            <span className="text-xs">{platform}:</span>
                            <span className="font-medium text-xs">{percentage.toFixed(1)}%</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-orange-50 to-yellow-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-orange-700 mb-2">Demographics</h4>
                      <div className="space-y-1">
                        {feature.demographicBreakdown.map((demo) => (
                          <div key={demo.ageGroup} className="flex justify-between">
                            <span className="text-xs">{demo.ageGroup}:</span>
                            <span className="font-medium text-xs">{demo.usage.toFixed(1)}%</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-700">Technical Requirements</p>
                        <p className="text-sm text-gray-600">
                          {feature.technicalRequirements.minDeviceSpecs} devices • 
                          {feature.technicalRequirements.averagePerformance.toFixed(1)}% avg performance
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <BarChart3 className="h-4 w-4 mr-1" />
                          Details
                        </Button>
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4 mr-1" />
                          Optimize
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="spatial" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Spatial Interaction Analytics</h2>
            <div className="flex gap-2">
              <Button variant="outline" className="flex items-center gap-2">
                <Move3d className="h-4 w-4" />
                3D Heatmap
              </Button>
              <Button className="flex items-center gap-2">
                <Navigation className="h-4 w-4" />
                Spatial Insights
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Interaction Heatmap
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ScatterChart data={spatialInteractions.slice(0, 30)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="coordinates.x" type="number" name="X Position" />
                    <YAxis dataKey="coordinates.z" type="number" name="Z Position" />
                    <Tooltip cursor={{ strokeDasharray: '3 3' }} />
                    <Scatter dataKey="intensity" fill="#8b5cf6" />
                  </ScatterChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Interaction Types Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Array.from(new Set(spatialInteractions.map(i => i.interactionType))).map((type) => {
                    const typeInteractions = spatialInteractions.filter(i => i.interactionType === type);
                    const avgDuration = typeInteractions.reduce((sum, i) => sum + i.duration, 0) / typeInteractions.length;
                    const avgIntensity = typeInteractions.reduce((sum, i) => sum + i.intensity, 0) / typeInteractions.length;
                    
                    return (
                      <div key={type} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 rounded-full bg-gradient-to-r from-indigo-400 to-purple-400" />
                          <div>
                            <p className="font-medium capitalize">{type}</p>
                            <p className="text-sm text-gray-600">{typeInteractions.length} interactions</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">{avgDuration.toFixed(1)}s</p>
                          <p className="text-sm text-gray-600">{Math.floor(avgIntensity)}% intensity</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Compass className="h-5 w-5" />
                Spatial Behavior Patterns
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-700 mb-3">Movement Patterns</h4>
                  <div className="space-y-2">
                    {Array.from(new Set(productSessions.map(s => s.spatialData.movementPattern))).map((pattern) => {
                      const count = productSessions.filter(s => s.spatialData.movementPattern === pattern).length;
                      const percentage = (count / productSessions.length) * 100;
                      return (
                        <div key={pattern} className="flex justify-between">
                          <span className="capitalize text-sm">{pattern.replace('_', ' ')}</span>
                          <span className="font-medium text-sm">{percentage.toFixed(1)}%</span>
                        </div>
                      );
                    })}
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-green-700 mb-3">Viewing Behavior</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Avg Distance:</span>
                      <span className="font-medium">
                        {(productSessions.reduce((sum, s) => sum + s.spatialData.distanceFromProduct.reduce((a, b) => a + b, 0) / s.spatialData.distanceFromProduct.length, 0) / productSessions.length).toFixed(1)}m
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Room Scan Accuracy:</span>
                      <span className="font-medium">
                        {(productSessions.reduce((sum, s) => sum + s.spatialData.roomScanAccuracy, 0) / productSessions.length).toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Placement Attempts:</span>
                      <span className="font-medium">
                        {(productSessions.reduce((sum, s) => sum + s.spatialData.placementAttempts, 0) / productSessions.length).toFixed(1)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-purple-700 mb-3">Interaction Outcomes</h4>
                  <div className="space-y-2">
                    {Array.from(new Set(spatialInteractions.map(i => i.outcome))).map((outcome) => {
                      const count = spatialInteractions.filter(i => i.outcome === outcome).length;
                      const percentage = (count / spatialInteractions.length) * 100;
                      return (
                        <div key={outcome} className="flex justify-between">
                          <span className="capitalize text-sm">{outcome}</span>
                          <span className="font-medium text-sm">{percentage.toFixed(1)}%</span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gauge className="h-5 w-5" />
                  Technical Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RadarChart data={[
                    {
                      metric: 'Frame Rate',
                      value: productSessions.reduce((sum, s) => sum + s.technicalMetrics.frameRate, 0) / productSessions.length
                    },
                    {
                      metric: 'Load Time',
                      value: 100 - (productSessions.reduce((sum, s) => sum + s.technicalMetrics.loadTime, 0) / productSessions.length * 20)
                    },
                    {
                      metric: 'Tracking',
                      value: productSessions.reduce((sum, s) => sum + s.technicalMetrics.trackingAccuracy, 0) / productSessions.length
                    },
                    {
                      metric: 'Occlusion',
                      value: productSessions.reduce((sum, s) => sum + s.technicalMetrics.occlusionHandling, 0) / productSessions.length
                    },
                    {
                      metric: 'Lighting',
                      value: productSessions.reduce((sum, s) => sum + s.technicalMetrics.lightingRealism, 0) / productSessions.length
                    }
                  ]}>
                    <PolarGrid />
                    <PolarAngleAxis dataKey="metric" />
                    <PolarRadiusAxis angle={90} domain={[0, 100]} />
                    <Radar dataKey="value" stroke="#8b5cf6" fill="#8b5cf6" fillOpacity={0.6} />
                  </RadarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="h-5 w-5" />
                  User Experience Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsBarChart data={experienceMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="completionRate" fill="#10b981" />
                    <Bar dataKey="userSatisfaction" fill="#f59e0b" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Real-time Performance Monitoring
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {[
                  { label: 'Avg Frame Rate', value: `${Math.floor(productSessions.reduce((sum, s) => sum + s.technicalMetrics.frameRate, 0) / productSessions.length)} FPS`, color: 'text-green-600' },
                  { label: 'Load Time', value: `${(productSessions.reduce((sum, s) => sum + s.technicalMetrics.loadTime, 0) / productSessions.length).toFixed(1)}s`, color: 'text-blue-600' },
                  { label: 'Tracking Accuracy', value: `${Math.floor(productSessions.reduce((sum, s) => sum + s.technicalMetrics.trackingAccuracy, 0) / productSessions.length)}%`, color: 'text-purple-600' },
                  { label: 'User Satisfaction', value: `${(productSessions.reduce((sum, s) => sum + s.userFeedback.experienceRating, 0) / productSessions.length).toFixed(1)}/10`, color: 'text-yellow-600' },
                  { label: 'Completion Rate', value: `${Math.floor(productSessions.filter(s => s.outcome.completedExperience).length / productSessions.length * 100)}%`, color: 'text-cyan-600' },
                  { label: 'Error Rate', value: `${(Math.random() * 3 + 1).toFixed(1)}%`, color: 'text-red-600' }
                ].map((metric, idx) => (
                  <div key={idx} className="text-center p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg">
                    <p className="text-sm text-gray-600">{metric.label}</p>
                    <p className={`text-2xl font-bold ${metric.color}`}>{metric.value}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Experience Insights</h2>
            <Button className="flex items-center gap-2 bg-gradient-to-r from-indigo-500 to-purple-500">
              <Brain className="h-4 w-4" />
              Generate Insights
            </Button>
          </div>

          <div className="grid gap-4">
            {experienceInsights.map((insight) => (
              <Card key={insight.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <Badge variant="outline" className="capitalize">
                          {insight.category.replace('_', ' ')}
                        </Badge>
                        <Badge variant={
                          insight.impact === 'critical' ? 'destructive' :
                          insight.impact === 'high' ? 'default' :
                          insight.impact === 'medium' ? 'secondary' :
                          'outline'
                        }>
                          {insight.impact} impact
                        </Badge>
                        <Badge variant="outline">{Math.floor(insight.confidence)}% confidence</Badge>
                        {insight.productId && (
                          <Badge variant="outline" className="border-indigo-500 text-indigo-700">
                            Product Specific
                          </Badge>
                        )}
                      </div>
                      <h3 className="text-lg font-semibold mb-2">{insight.insight}</h3>
                      <p className="text-gray-600 mb-4">{insight.recommendation}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <h4 className="font-semibold text-gray-700 mb-2">Supporting Data</h4>
                          <div className="space-y-1">
                            {insight.supportingData.map((data, idx) => (
                              <div key={idx} className="flex justify-between text-sm">
                                <span>{data.metric}:</span>
                                <span className={`font-medium ${
                                  data.trend === 'up' ? 'text-green-600' : 
                                  data.trend === 'down' ? 'text-red-600' : 
                                  'text-gray-600'
                                }`}>
                                  {data.value.toFixed(1)}
                                  {data.trend === 'up' ? ' ↗' : data.trend === 'down' ? ' ↘' : ' →'}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h4 className="font-semibold text-gray-700 mb-2">Implementation</h4>
                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span>Effort:</span>
                              <span className={`font-medium capitalize ${
                                insight.implementation.effort === 'high' ? 'text-red-600' :
                                insight.implementation.effort === 'medium' ? 'text-yellow-600' :
                                'text-green-600'
                              }`}>
                                {insight.implementation.effort}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Timeframe:</span>
                              <span className="font-medium">{insight.implementation.timeframe}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Expected ROI:</span>
                              <span className="font-medium text-green-600">{insight.implementation.expectedROI}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 p-4 rounded-lg">
                        <h4 className="font-semibold text-indigo-700 mb-2">Potential Impact</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-gray-600">Affected Sessions:</p>
                            <p className="font-medium">{insight.affectedSessions.toLocaleString()}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Business Impact:</p>
                            <p className="font-medium text-green-600">{insight.potentialImpact}</p>
                          </div>
                        </div>
                        <div className="mt-2">
                          <p className="text-gray-600 text-sm">Resources: {insight.implementation.resources.join(', ')}</p>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button size="sm" className="flex items-center gap-1">
                        <CheckCircle className="h-4 w-4" />
                        Implement
                      </Button>
                      <Button variant="outline" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default VirtualProductExperience;