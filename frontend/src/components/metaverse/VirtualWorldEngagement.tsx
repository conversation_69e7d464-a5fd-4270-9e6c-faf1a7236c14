/**
 * Virtual World Engagement Metrics
 * Advanced analytics for immersive virtual environment interactions and user behavior
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Globe,
  Users,
  Eye,
  Hand,
  Gamepad2,
  Headphones,
  Monitor,
  Activity,
  Clock,
  Target,
  TrendingUp,
  TrendingDown,
  Zap,
  Brain,
  Network,
  Building2,
  Home,
  Crown,
  Star,
  Heart,
  MessageCircle,
  Share,
  ThumbsUp,
  Award,
  Trophy,
  Gem,
  Sparkles,
  Wand2,
  Move3d,
  Navigation,
  Compass,
  MapPin,
  Route,
  Layers,
  Box,
  Cube,
  Palette,
  Brush,
  Camera,
  Video,
  Music,
  Image,
  Volume2,
  Mic,
  Speaker,
  Play,
  Pause,
  Stop,
  RotateCw,
  Maximize,
  Minimize,
  Settings,
  RefreshCw,
  Search,
  Filter,
  Download,
  Upload,
  BarChart3,
  LineChart,
  PieChart,
  Gauge,
  Plus,
  Minus,
  Edit,
  Trash,
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  ArrowLeft,
  ExternalLink,
  Link2,
  Unlock,
  Key,
  Shield,
  Lock,
  CheckCircle,
  AlertTriangle,
  Info,
  HelpCircle,
  BookOpen,
  FileText,
  File,
  Folder,
  Archive,
  Save,
  Mail,
  Phone,
  Bell,
  Calendar,
  DollarSign,
  CreditCard,
  ShoppingCart,
  ShoppingBag,
  Wallet,
  Store,
  Factory,
  Warehouse,
  School,
  Hospital,
  Library,
  Theater,
  Coffee,
  Utensils,
  Car,
  Plane,
  Rocket,
  TreePine,
  Mountain,
  Waves,
  Sun,
  Moon,
  Cloud,
  Wifi,
  Signal,
  Battery,
  Power,
  Smartphone,
  Tablet,
  Watch,
  Laptop,
  Database,
  Server,
  Cpu,
  Lightbulb,
  Focus,
  Crosshair
} from 'lucide-react';
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart as RechartsBarChart, Bar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ScatterChart, Scatter, ComposedChart, Cell, Treemap, Sankey, PieChart as RechartsPieChart, Pie } from 'recharts';

// TypeScript interfaces for virtual world engagement
interface VirtualWorld {
  id: string;
  name: string;
  type: 'social' | 'gaming' | 'education' | 'commerce' | 'entertainment' | 'professional' | 'mixed';
  platform: 'VR' | 'AR' | 'WebVR' | 'Mobile' | 'Desktop' | 'Cross-platform';
  environment: {
    theme: string;
    size: 'small' | 'medium' | 'large' | 'massive';
    capacity: number;
    instanceType: 'public' | 'private' | 'semi-private';
    accessibility: string[];
  };
  features: {
    voiceChat: boolean;
    textChat: boolean;
    gestureControls: boolean;
    hapticFeedback: boolean;
    spatialAudio: boolean;
    avatarCustomization: boolean;
    worldBuilding: boolean;
    economics: boolean;
  };
  demographics: {
    primaryAgeGroup: string;
    genderDistribution: { male: number; female: number; other: number };
    geographicDistribution: Record<string, number>;
    experienceLevel: Record<string, number>;
  };
  technicalSpecs: {
    minSystemRequirements: string;
    recommendedSpecs: string;
    supportedDevices: string[];
    networkRequirements: string;
    storageRequirements: string;
  };
}

interface VirtualWorldSession {
  sessionId: string;
  userId: string;
  worldId: string;
  avatar: {
    id: string;
    appearance: Record<string, any>;
    accessories: string[];
    customizations: number;
    uniquenessScore: number;
  };
  sessionMetrics: {
    startTime: string;
    endTime: string;
    duration: number;
    activeDuration: number;
    idleDuration: number;
    reconnections: number;
    qualityDrops: number;
  };
  spatialEngagement: {
    locationsVisited: number;
    distanceTraveled: number;
    teleportations: number;
    collisions: number;
    boundaryHits: number;
    heightChanges: number;
    favoriteLocations: string[];
    dwellTimes: Record<string, number>;
  };
  socialInteractions: {
    usersEncountered: number;
    conversationsInitiated: number;
    messagesExchanged: number;
    friendRequestsSent: number;
    friendRequestsReceived: number;
    groupsJoined: number;
    eventsAttended: number;
    collaborativeTasks: number;
  };
  objectInteractions: {
    objectsManipulated: number;
    creationsBuilt: number;
    destructionActions: number;
    shareableContent: number;
    collectiblesFound: number;
    puzzlesSolved: number;
    achievementsUnlocked: number;
    skillsLearned: number;
  };
  economicActivity: {
    virtualCurrencyEarned: number;
    virtualCurrencySpent: number;
    itemsPurchased: number;
    itemsSold: number;
    servicesUsed: number;
    subscriptionsActive: number;
    transactionValue: number;
    economicRank: number;
  };
  contentEngagement: {
    mediaConsumed: number;
    contentCreated: number;
    contentShared: number;
    likesGiven: number;
    commentsPosted: number;
    reviewsWritten: number;
    tutorialsCompleted: number;
    knowledgeSharing: number;
  };
  emotionalMetrics: {
    enjoymentLevel: number;
    frustrationLevel: number;
    socialFulfillment: number;
    accomplishmentSense: number;
    immersionDepth: number;
    comfortLevel: number;
    curiosityEngagement: number;
    moodChange: number;
  };
  technicalPerformance: {
    frameRate: number;
    latency: number;
    bandwidth: number;
    systemLoad: number;
    renderQuality: number;
    audioQuality: number;
    controlResponsiveness: number;
    stabilityScore: number;
  };
  behaviorPatterns: {
    explorationStyle: 'methodical' | 'random' | 'social' | 'goal-oriented' | 'creative';
    learningCurve: number;
    adaptationSpeed: number;
    riskTaking: number;
    socialOpenness: number;
    creativityLevel: number;
    competitiveness: number;
    cooperativeness: number;
  };
}

interface EngagementMetric {
  timestamp: string;
  activeUsers: number;
  averageSessionTime: number;
  socialInteractions: number;
  contentCreation: number;
  economicTransactions: number;
  userSatisfaction: number;
  technicalQuality: number;
  retentionRate: number;
  newUserOnboarding: number;
  crossPlatformActivity: number;
}

interface WorldAnalytics {
  worldId: string;
  worldName: string;
  timeframe: string;
  userMetrics: {
    totalUsers: number;
    activeUsers: number;
    newUsers: number;
    returningUsers: number;
    averageSessionDuration: number;
    sessionsPerUser: number;
    userRetentionRate: number;
    churnRate: number;
  };
  engagementMetrics: {
    totalInteractions: number;
    socialEngagement: number;
    contentCreation: number;
    economicActivity: number;
    learningProgress: number;
    achievementsUnlocked: number;
    collaborativeProjects: number;
    userGeneratedContent: number;
  };
  spatialMetrics: {
    popularLocations: { name: string; visits: number; dwellTime: number }[];
    trafficFlow: { from: string; to: string; count: number }[];
    utilizationHeatmap: { x: number; y: number; z: number; density: number }[];
    crowdedAreas: string[];
    underutilizedSpaces: string[];
  };
  economicMetrics: {
    totalTransactions: number;
    virtualCurrencyCirculation: number;
    averageTransactionValue: number;
    topPurchasedItems: string[];
    economicGrowthRate: number;
    wealthDistribution: { segment: string; percentage: number }[];
  };
  socialMetrics: {
    friendshipFormations: number;
    groupCreations: number;
    communityEvents: number;
    conflictResolutions: number;
    mentorshipConnections: number;
    culturalExchanges: number;
    leadershipEmergence: number;
  };
  contentMetrics: {
    userCreatedContent: number;
    sharedExperiences: number;
    collaborativeCreations: number;
    educationalContent: number;
    entertainmentContent: number;
    commercialContent: number;
    qualityScore: number;
  };
  technicalMetrics: {
    averageFrameRate: number;
    averageLatency: number;
    systemStability: number;
    crossPlatformCompatibility: number;
    devicePerformance: Record<string, number>;
    networkEfficiency: number;
    scalabilityScore: number;
  };
}

interface UserEngagementProfile {
  userId: string;
  userType: 'explorer' | 'socializer' | 'creator' | 'achiever' | 'learner' | 'entrepreneur' | 'entertainer';
  engagementScore: number;
  preferredWorlds: string[];
  sessionPatterns: {
    preferredTimes: number[];
    sessionFrequency: string;
    averageDuration: number;
    platformPreference: string;
    devicePreference: string;
  };
  socialProfile: {
    networkSize: number;
    influenceScore: number;
    collaborationIndex: number;
    leadershipRating: number;
    mentorshipActivity: number;
    communityContribution: number;
  };
  skillDevelopment: {
    technicalSkills: Record<string, number>;
    creativeSkills: Record<string, number>;
    socialSkills: Record<string, number>;
    businessSkills: Record<string, number>;
    learningVelocity: number;
    knowledgeSharing: number;
  };
  economicBehavior: {
    spendingPattern: string;
    earningCapacity: number;
    investmentStrategy: string;
    riskTolerance: number;
    entrepreneurialActivity: number;
    economicInfluence: number;
  };
  contentActivity: {
    creationFrequency: number;
    sharingBehavior: number;
    qualityRating: number;
    originalityScore: number;
    collaborationLevel: number;
    teachingActivity: number;
  };
  wellbeingIndicators: {
    satisfactionLevel: number;
    stressLevel: number;
    socialConnectedness: number;
    purposeFulfillment: number;
    growthMindset: number;
    balanceScore: number;
  };
}

interface EngagementInsight {
  id: string;
  category: 'user_behavior' | 'social_dynamics' | 'content_strategy' | 'technical_optimization' | 'economic_balance';
  insight: string;
  worldContext: string;
  dataPoints: number;
  confidence: number;
  impact: 'low' | 'medium' | 'high' | 'critical';
  recommendation: string;
  implementation: {
    complexity: 'simple' | 'moderate' | 'complex';
    resources: string[];
    timeframe: string;
    expectedOutcome: string;
    successMetrics: string[];
  };
  affectedUserSegments: string[];
  businessValue: string;
  priority: number;
  createdAt: string;
}

// Mock data generation functions
const generateVirtualWorlds = (): VirtualWorld[] => {
  const worldTypes = ['social', 'gaming', 'education', 'commerce', 'entertainment', 'professional', 'mixed'] as const;
  const platforms = ['VR', 'AR', 'WebVR', 'Mobile', 'Desktop', 'Cross-platform'] as const;
  const themes = ['Fantasy', 'Sci-Fi', 'Modern', 'Historical', 'Abstract', 'Nature', 'Urban', 'Space'];
  
  return Array.from({ length: 8 }, (_, i) => ({
    id: `world-${i + 1}`,
    name: `${themes[i % themes.length]} World ${i + 1}`,
    type: worldTypes[i % worldTypes.length],
    platform: platforms[i % platforms.length],
    environment: {
      theme: themes[i % themes.length],
      size: ['small', 'medium', 'large', 'massive'][Math.floor(Math.random() * 4)] as any,
      capacity: Math.floor(Math.random() * 1000) + 100,
      instanceType: ['public', 'private', 'semi-private'][Math.floor(Math.random() * 3)] as any,
      accessibility: ['Wheelchair', 'Audio Assistance', 'Visual Aids', 'Simplified Controls'].slice(0, Math.floor(Math.random() * 3) + 1)
    },
    features: {
      voiceChat: Math.random() > 0.3,
      textChat: Math.random() > 0.1,
      gestureControls: Math.random() > 0.4,
      hapticFeedback: Math.random() > 0.6,
      spatialAudio: Math.random() > 0.2,
      avatarCustomization: Math.random() > 0.2,
      worldBuilding: Math.random() > 0.7,
      economics: Math.random() > 0.5
    },
    demographics: {
      primaryAgeGroup: ['13-17', '18-24', '25-34', '35-44', '45-54', '55+'][Math.floor(Math.random() * 6)],
      genderDistribution: {
        male: Math.random() * 60 + 20,
        female: Math.random() * 60 + 20,
        other: Math.random() * 20
      },
      geographicDistribution: {
        'North America': Math.random() * 40 + 20,
        'Europe': Math.random() * 30 + 15,
        'Asia': Math.random() * 35 + 20,
        'Other': Math.random() * 25 + 5
      },
      experienceLevel: {
        'Beginner': Math.random() * 40 + 10,
        'Intermediate': Math.random() * 40 + 20,
        'Advanced': Math.random() * 30 + 15,
        'Expert': Math.random() * 20 + 5
      }
    },
    technicalSpecs: {
      minSystemRequirements: 'Mid-range mobile device or PC',
      recommendedSpecs: 'High-end VR headset with dedicated GPU',
      supportedDevices: ['VR Headset', 'Smartphone', 'PC', 'Tablet'].slice(0, Math.floor(Math.random() * 3) + 1),
      networkRequirements: 'Broadband internet connection (10+ Mbps)',
      storageRequirements: `${Math.floor(Math.random() * 50) + 5}GB`
    }
  }));
};

const generateEngagementMetrics = (): EngagementMetric[] => {
  return Array.from({ length: 24 }, (_, i) => ({
    timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString().split('T')[1].split(':')[0] + ':00',
    activeUsers: Math.floor(Math.random() * 1000) + 200,
    averageSessionTime: Math.random() * 120 + 30,
    socialInteractions: Math.floor(Math.random() * 500) + 100,
    contentCreation: Math.floor(Math.random() * 200) + 50,
    economicTransactions: Math.floor(Math.random() * 100) + 20,
    userSatisfaction: Math.random() * 30 + 70,
    technicalQuality: Math.random() * 25 + 75,
    retentionRate: Math.random() * 40 + 60,
    newUserOnboarding: Math.floor(Math.random() * 50) + 10,
    crossPlatformActivity: Math.random() * 100
  }));
};

const generateWorldAnalytics = (): WorldAnalytics[] => {
  return Array.from({ length: 6 }, (_, i) => ({
    worldId: `world-${i + 1}`,
    worldName: `Virtual World ${i + 1}`,
    timeframe: 'Last 30 Days',
    userMetrics: {
      totalUsers: Math.floor(Math.random() * 50000) + 10000,
      activeUsers: Math.floor(Math.random() * 20000) + 5000,
      newUsers: Math.floor(Math.random() * 5000) + 1000,
      returningUsers: Math.floor(Math.random() * 15000) + 3000,
      averageSessionDuration: Math.random() * 180 + 30,
      sessionsPerUser: Math.random() * 10 + 2,
      userRetentionRate: Math.random() * 40 + 60,
      churnRate: Math.random() * 20 + 5
    },
    engagementMetrics: {
      totalInteractions: Math.floor(Math.random() * 1000000) + 200000,
      socialEngagement: Math.floor(Math.random() * 100000) + 20000,
      contentCreation: Math.floor(Math.random() * 50000) + 10000,
      economicActivity: Math.floor(Math.random() * 20000) + 5000,
      learningProgress: Math.floor(Math.random() * 30000) + 5000,
      achievementsUnlocked: Math.floor(Math.random() * 40000) + 8000,
      collaborativeProjects: Math.floor(Math.random() * 5000) + 1000,
      userGeneratedContent: Math.floor(Math.random() * 15000) + 3000
    },
    spatialMetrics: {
      popularLocations: [
        { name: 'Central Plaza', visits: Math.floor(Math.random() * 10000) + 2000, dwellTime: Math.random() * 300 + 60 },
        { name: 'Creative Studio', visits: Math.floor(Math.random() * 8000) + 1500, dwellTime: Math.random() * 600 + 120 },
        { name: 'Social Hub', visits: Math.floor(Math.random() * 12000) + 3000, dwellTime: Math.random() * 400 + 80 },
        { name: 'Learning Center', visits: Math.floor(Math.random() * 6000) + 1000, dwellTime: Math.random() * 800 + 200 }
      ],
      trafficFlow: [
        { from: 'Entry Point', to: 'Central Plaza', count: Math.floor(Math.random() * 5000) + 1000 },
        { from: 'Central Plaza', to: 'Social Hub', count: Math.floor(Math.random() * 4000) + 800 },
        { from: 'Social Hub', to: 'Creative Studio', count: Math.floor(Math.random() * 3000) + 600 }
      ],
      utilizationHeatmap: Array.from({ length: 20 }, () => ({
        x: Math.random() * 100,
        y: Math.random() * 100,
        z: Math.random() * 50,
        density: Math.random() * 100
      })),
      crowdedAreas: ['Central Plaza', 'Event Stage', 'Marketplace'].slice(0, Math.floor(Math.random() * 2) + 1),
      underutilizedSpaces: ['Quiet Zone', 'Research Lab', 'Archive'].slice(0, Math.floor(Math.random() * 2) + 1)
    },
    economicMetrics: {
      totalTransactions: Math.floor(Math.random() * 100000) + 20000,
      virtualCurrencyCirculation: Math.floor(Math.random() * 10000000) + 2000000,
      averageTransactionValue: Math.random() * 50 + 10,
      topPurchasedItems: ['Avatar Accessories', 'Virtual Real Estate', 'Premium Tools', 'Digital Art'].slice(0, Math.floor(Math.random() * 3) + 1),
      economicGrowthRate: Math.random() * 30 + 5,
      wealthDistribution: [
        { segment: 'Top 1%', percentage: Math.random() * 30 + 20 },
        { segment: 'Top 10%', percentage: Math.random() * 40 + 30 },
        { segment: 'Middle 50%', percentage: Math.random() * 30 + 35 },
        { segment: 'Bottom 39%', percentage: Math.random() * 20 + 10 }
      ]
    },
    socialMetrics: {
      friendshipFormations: Math.floor(Math.random() * 10000) + 2000,
      groupCreations: Math.floor(Math.random() * 1000) + 200,
      communityEvents: Math.floor(Math.random() * 100) + 20,
      conflictResolutions: Math.floor(Math.random() * 500) + 50,
      mentorshipConnections: Math.floor(Math.random() * 800) + 100,
      culturalExchanges: Math.floor(Math.random() * 300) + 50,
      leadershipEmergence: Math.floor(Math.random() * 100) + 10
    },
    contentMetrics: {
      userCreatedContent: Math.floor(Math.random() * 20000) + 5000,
      sharedExperiences: Math.floor(Math.random() * 15000) + 3000,
      collaborativeCreations: Math.floor(Math.random() * 5000) + 800,
      educationalContent: Math.floor(Math.random() * 3000) + 500,
      entertainmentContent: Math.floor(Math.random() * 8000) + 1500,
      commercialContent: Math.floor(Math.random() * 2000) + 300,
      qualityScore: Math.random() * 30 + 70
    },
    technicalMetrics: {
      averageFrameRate: Math.random() * 30 + 60,
      averageLatency: Math.random() * 50 + 20,
      systemStability: Math.random() * 20 + 80,
      crossPlatformCompatibility: Math.random() * 25 + 75,
      devicePerformance: {
        'VR Headset': Math.random() * 30 + 70,
        'PC': Math.random() * 25 + 75,
        'Mobile': Math.random() * 40 + 60,
        'Tablet': Math.random() * 35 + 65
      },
      networkEfficiency: Math.random() * 20 + 80,
      scalabilityScore: Math.random() * 30 + 70
    }
  }));
};

const generateUserEngagementProfiles = (): UserEngagementProfile[] => {
  const userTypes = ['explorer', 'socializer', 'creator', 'achiever', 'learner', 'entrepreneur', 'entertainer'] as const;
  
  return Array.from({ length: 12 }, (_, i) => ({
    userId: `user-${i + 1}`,
    userType: userTypes[i % userTypes.length],
    engagementScore: Math.random() * 40 + 60,
    preferredWorlds: [`World ${(i % 3) + 1}`, `World ${(i % 4) + 2}`],
    sessionPatterns: {
      preferredTimes: Array.from({ length: Math.floor(Math.random() * 4) + 2 }, () => Math.floor(Math.random() * 24)),
      sessionFrequency: ['Daily', 'Weekly', 'Monthly'][Math.floor(Math.random() * 3)],
      averageDuration: Math.random() * 180 + 30,
      platformPreference: ['VR', 'Desktop', 'Mobile'][Math.floor(Math.random() * 3)],
      devicePreference: ['High-end', 'Mid-range', 'Entry-level'][Math.floor(Math.random() * 3)]
    },
    socialProfile: {
      networkSize: Math.floor(Math.random() * 500) + 50,
      influenceScore: Math.random() * 100,
      collaborationIndex: Math.random() * 100,
      leadershipRating: Math.random() * 100,
      mentorshipActivity: Math.random() * 100,
      communityContribution: Math.random() * 100
    },
    skillDevelopment: {
      technicalSkills: {
        'Programming': Math.random() * 100,
        '3D Modeling': Math.random() * 100,
        'Animation': Math.random() * 100,
        'UI/UX Design': Math.random() * 100
      },
      creativeSkills: {
        'Artistic Design': Math.random() * 100,
        'Storytelling': Math.random() * 100,
        'Music Creation': Math.random() * 100,
        'Writing': Math.random() * 100
      },
      socialSkills: {
        'Communication': Math.random() * 100,
        'Leadership': Math.random() * 100,
        'Collaboration': Math.random() * 100,
        'Conflict Resolution': Math.random() * 100
      },
      businessSkills: {
        'Marketing': Math.random() * 100,
        'Sales': Math.random() * 100,
        'Project Management': Math.random() * 100,
        'Entrepreneurship': Math.random() * 100
      },
      learningVelocity: Math.random() * 100,
      knowledgeSharing: Math.random() * 100
    },
    economicBehavior: {
      spendingPattern: ['Conservative', 'Moderate', 'Aggressive'][Math.floor(Math.random() * 3)],
      earningCapacity: Math.random() * 100,
      investmentStrategy: ['Safe', 'Balanced', 'Risky'][Math.floor(Math.random() * 3)],
      riskTolerance: Math.random() * 100,
      entrepreneurialActivity: Math.random() * 100,
      economicInfluence: Math.random() * 100
    },
    contentActivity: {
      creationFrequency: Math.random() * 100,
      sharingBehavior: Math.random() * 100,
      qualityRating: Math.random() * 30 + 70,
      originalityScore: Math.random() * 100,
      collaborationLevel: Math.random() * 100,
      teachingActivity: Math.random() * 100
    },
    wellbeingIndicators: {
      satisfactionLevel: Math.random() * 30 + 70,
      stressLevel: Math.random() * 40 + 10,
      socialConnectedness: Math.random() * 40 + 60,
      purposeFulfillment: Math.random() * 40 + 60,
      growthMindset: Math.random() * 30 + 70,
      balanceScore: Math.random() * 40 + 60
    }
  }));
};

const generateEngagementInsights = (): EngagementInsight[] => {
  const categories = ['user_behavior', 'social_dynamics', 'content_strategy', 'technical_optimization', 'economic_balance'] as const;
  const worlds = ['Fantasy World', 'Sci-Fi Space', 'Social Hub', 'Learning Center', 'Creative Studio'];
  
  return Array.from({ length: 12 }, (_, i) => ({
    id: `engagement-insight-${i + 1}`,
    category: categories[i % categories.length],
    insight: [
      'Users with avatar customization spend 45% more time in social spaces',
      'Cross-platform users show 32% higher retention rates',
      'Voice chat availability increases collaborative project success by 28%',
      'Peak engagement occurs during community events with 67% participation',
      'New user onboarding completion correlates with 85% higher long-term retention',
      'Creator-type users generate 3x more engagement than average users',
      'Economic activity drives 40% of return visits in commerce-focused worlds',
      'Technical performance issues reduce user satisfaction by 55%',
      'Social connections formed in first 24 hours predict 6-month retention',
      'Educational content with gamification shows 78% completion rates',
      'User-generated content receives 4x more engagement than platform content',
      'Multi-device accessibility increases user base by 35%'
    ][i],
    worldContext: worlds[i % worlds.length],
    dataPoints: Math.floor(Math.random() * 100000) + 10000,
    confidence: Math.random() * 30 + 70,
    impact: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
    recommendation: [
      'Expand avatar customization options and social features',
      'Invest in cross-platform compatibility improvements',
      'Implement advanced voice chat with spatial audio',
      'Increase frequency and variety of community events',
      'Redesign onboarding flow with interactive tutorials',
      'Develop creator tools and monetization features',
      'Add economic incentives and virtual marketplace',
      'Upgrade technical infrastructure and monitoring',
      'Create social matchmaking and introduction systems',
      'Gamify educational content with progress tracking',
      'Promote user-generated content with featured showcases',
      'Develop unified multi-device experience'
    ][i],
    implementation: {
      complexity: ['simple', 'moderate', 'complex'][Math.floor(Math.random() * 3)] as any,
      resources: ['Development Team', 'Design Team', 'Infrastructure', 'Community Management'].slice(0, Math.floor(Math.random() * 3) + 1),
      timeframe: ['2-4 weeks', '1-3 months', '3-6 months', '6-12 months'][Math.floor(Math.random() * 4)],
      expectedOutcome: `${Math.floor(Math.random() * 50) + 10}% improvement in key metrics`,
      successMetrics: ['User Engagement', 'Retention Rate', 'Satisfaction Score', 'Revenue Growth'].slice(0, Math.floor(Math.random() * 3) + 1)
    },
    affectedUserSegments: ['New Users', 'Power Users', 'Creators', 'Social Users', 'Learners'].slice(0, Math.floor(Math.random() * 4) + 1),
    businessValue: `$${Math.floor(Math.random() * 500000) + 100000} potential annual impact`,
    priority: Math.floor(Math.random() * 10) + 1,
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const VirtualWorldEngagement: React.FC = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('24h');
  const [selectedWorld, setSelectedWorld] = useState('all');
  const [selectedUserType, setSelectedUserType] = useState('all');
  const [viewMode, setViewMode] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');

  // Generated data
  const virtualWorlds = useMemo(() => generateVirtualWorlds(), []);
  const engagementMetrics = useMemo(() => generateEngagementMetrics(), []);
  const worldAnalytics = useMemo(() => generateWorldAnalytics(), []);
  const userProfiles = useMemo(() => generateUserEngagementProfiles(), []);
  const engagementInsights = useMemo(() => generateEngagementInsights(), []);

  // Filtering and processing
  const filteredInsights = useMemo(() => {
    return engagementInsights.filter(insight => 
      insight.insight.toLowerCase().includes(searchTerm.toLowerCase()) ||
      insight.worldContext.toLowerCase().includes(searchTerm.toLowerCase()) ||
      insight.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [engagementInsights, searchTerm]);

  const aggregatedMetrics = useMemo(() => {
    const analytics = selectedWorld === 'all' ? worldAnalytics : worldAnalytics.filter(a => a.worldId === selectedWorld);
    
    return {
      totalUsers: analytics.reduce((sum, a) => sum + a.userMetrics.totalUsers, 0),
      averageEngagement: analytics.reduce((sum, a) => sum + a.engagementMetrics.totalInteractions, 0) / analytics.length,
      averageRetention: analytics.reduce((sum, a) => sum + a.userMetrics.userRetentionRate, 0) / analytics.length,
      totalContent: analytics.reduce((sum, a) => sum + a.contentMetrics.userCreatedContent, 0),
      economicActivity: analytics.reduce((sum, a) => sum + a.economicMetrics.totalTransactions, 0)
    };
  }, [worldAnalytics, selectedWorld]);

  const userTypeDistribution = useMemo(() => {
    const distribution = userProfiles.reduce((acc, profile) => {
      acc[profile.userType] = (acc[profile.userType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return Object.entries(distribution).map(([type, count]) => ({
      type: type.charAt(0).toUpperCase() + type.slice(1),
      count,
      percentage: (count / userProfiles.length) * 100
    }));
  }, [userProfiles]);

  const getInsightIcon = (category: string) => {
    switch (category) {
      case 'user_behavior': return <Activity className="h-4 w-4" />;
      case 'social_dynamics': return <Users className="h-4 w-4" />;
      case 'content_strategy': return <Palette className="h-4 w-4" />;
      case 'technical_optimization': return <Settings className="h-4 w-4" />;
      case 'economic_balance': return <DollarSign className="h-4 w-4" />;
      default: return <Brain className="h-4 w-4" />;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getUserTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'explorer': return <Compass className="h-4 w-4" />;
      case 'socializer': return <Users className="h-4 w-4" />;
      case 'creator': return <Brush className="h-4 w-4" />;
      case 'achiever': return <Trophy className="h-4 w-4" />;
      case 'learner': return <BookOpen className="h-4 w-4" />;
      case 'entrepreneur': return <Lightbulb className="h-4 w-4" />;
      case 'entertainer': return <Star className="h-4 w-4" />;
      default: return <Users className="h-4 w-4" />;
    }
  };

  return (
    <div className="w-full space-y-6 p-6 bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 min-h-screen">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <Globe className="h-8 w-8 text-purple-600" />
            Virtual World Engagement
          </h1>
          <p className="text-gray-600 mt-1">Immersive environment analytics and user behavior insights</p>
        </div>
        
        <div className="flex flex-wrap items-center gap-3">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search insights..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
            />
          </div>
          
          <select
            value={selectedWorld}
            onChange={(e) => setSelectedWorld(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="all">All Worlds</option>
            {virtualWorlds.map(world => (
              <option key={world.id} value={world.id}>{world.name}</option>
            ))}
          </select>
          
          <select
            value={selectedUserType}
            onChange={(e) => setSelectedUserType(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="all">All User Types</option>
            <option value="explorer">Explorer</option>
            <option value="socializer">Socializer</option>
            <option value="creator">Creator</option>
            <option value="achiever">Achiever</option>
            <option value="learner">Learner</option>
            <option value="entrepreneur">Entrepreneur</option>
            <option value="entertainer">Entertainer</option>
          </select>
          
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm">Total Users</p>
                <p className="text-2xl font-bold">{(aggregatedMetrics.totalUsers / 1000).toFixed(0)}K</p>
                <p className="text-purple-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +15.2% vs last period
                </p>
              </div>
              <Users className="h-8 w-8 text-purple-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">Avg Engagement</p>
                <p className="text-2xl font-bold">{(aggregatedMetrics.averageEngagement / 1000).toFixed(0)}K</p>
                <p className="text-blue-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +22.7% vs last period
                </p>
              </div>
              <Activity className="h-8 w-8 text-blue-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm">Retention Rate</p>
                <p className="text-2xl font-bold">{aggregatedMetrics.averageRetention.toFixed(1)}%</p>
                <p className="text-green-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +4.8% vs last period
                </p>
              </div>
              <Target className="h-8 w-8 text-green-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm">User Content</p>
                <p className="text-2xl font-bold">{(aggregatedMetrics.totalContent / 1000).toFixed(0)}K</p>
                <p className="text-orange-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +18.9% vs last period
                </p>
              </div>
              <Palette className="h-8 w-8 text-orange-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-teal-500 to-teal-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-teal-100 text-sm">Economic Activity</p>
                <p className="text-2xl font-bold">{(aggregatedMetrics.economicActivity / 1000).toFixed(0)}K</p>
                <p className="text-teal-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +11.3% vs last period
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-teal-200" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={viewMode} onValueChange={setViewMode} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="worlds">World Analytics</TabsTrigger>
          <TabsTrigger value="users">User Profiles</TabsTrigger>
          <TabsTrigger value="insights">AI Insights</TabsTrigger>
          <TabsTrigger value="realtime">Real-time</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Hourly Engagement */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  24-Hour Engagement Pattern
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ComposedChart data={engagementMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Area yAxisId="left" type="monotone" dataKey="activeUsers" stackId="1" stroke="#8B5CF6" fill="#C4B5FD" />
                    <Line yAxisId="right" type="monotone" dataKey="userSatisfaction" stroke="#10B981" strokeWidth={2} />
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* User Type Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  User Type Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Tooltip />
                    <Radar dataKey="percentage" cx="50%" cy="50%" outerRadius={100} fill="#8B5CF6" />
                  </PieChart>
                </ResponsiveContainer>
                <div className="grid grid-cols-2 gap-2 mt-4">
                  {userTypeDistribution.map((item, i) => (
                    <div key={i} className="flex items-center gap-2 text-sm">
                      {getUserTypeIcon(item.type)}
                      <span className="text-gray-600">{item.type}</span>
                      <span className="font-medium">{item.percentage.toFixed(1)}%</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Social & Content Metrics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Social Interactions & Content Creation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <ComposedChart data={engagementMetrics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="timestamp" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Bar yAxisId="left" dataKey="socialInteractions" fill="#3B82F6" />
                  <Bar yAxisId="left" dataKey="contentCreation" fill="#8B5CF6" />
                  <Line yAxisId="right" type="monotone" dataKey="crossPlatformActivity" stroke="#F59E0B" strokeWidth={2} />
                </ComposedChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="worlds" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {worldAnalytics.map((world) => (
              <Card key={world.worldId} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="h-5 w-5" />
                    {world.worldName}
                  </CardTitle>
                  <p className="text-sm text-gray-600">{world.timeframe}</p>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* User Metrics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">User Activity</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-blue-50 p-3 rounded-lg text-center">
                        <p className="text-xl font-bold text-blue-600">{(world.userMetrics.totalUsers / 1000).toFixed(0)}K</p>
                        <p className="text-xs text-gray-600">Total Users</p>
                      </div>
                      <div className="bg-green-50 p-3 rounded-lg text-center">
                        <p className="text-xl font-bold text-green-600">{(world.userMetrics.activeUsers / 1000).toFixed(0)}K</p>
                        <p className="text-xs text-gray-600">Active Users</p>
                      </div>
                      <div className="bg-purple-50 p-3 rounded-lg text-center">
                        <p className="text-xl font-bold text-purple-600">{world.userMetrics.userRetentionRate.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Retention</p>
                      </div>
                      <div className="bg-orange-50 p-3 rounded-lg text-center">
                        <p className="text-xl font-bold text-orange-600">{Math.round(world.userMetrics.averageSessionDuration)}m</p>
                        <p className="text-xs text-gray-600">Avg Session</p>
                      </div>
                    </div>
                  </div>

                  {/* Engagement Metrics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Engagement Breakdown</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Social Engagement</span>
                        <span className="font-medium">{(world.engagementMetrics.socialEngagement / 1000).toFixed(0)}K</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Content Creation</span>
                        <span className="font-medium">{(world.engagementMetrics.contentCreation / 1000).toFixed(0)}K</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Economic Activity</span>
                        <span className="font-medium">{(world.economicMetrics.totalTransactions / 1000).toFixed(0)}K</span>
                      </div>
                    </div>
                  </div>

                  {/* Popular Locations */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Popular Locations</h4>
                    <div className="space-y-2">
                      {world.spatialMetrics.popularLocations.slice(0, 3).map((location, i) => (
                        <div key={i} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-gray-400" />
                            <span className="text-sm">{location.name}</span>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium">{location.visits.toLocaleString()}</p>
                            <p className="text-xs text-gray-500">{Math.round(location.dwellTime)}s avg</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Button variant="outline" className="w-full">
                    <Eye className="h-4 w-4 mr-2" />
                    View Detailed Analytics
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {userProfiles.map((profile) => (
              <Card key={profile.userId} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      {getUserTypeIcon(profile.userType)}
                      User {profile.userId.split('-')[1]}
                    </CardTitle>
                    <Badge variant="outline" className="capitalize">
                      {profile.userType}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-purple-600 h-2 rounded-full" 
                        style={{ width: `${profile.engagementScore}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">{profile.engagementScore.toFixed(0)}%</span>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Session Patterns */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Session Patterns</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-gray-600">Frequency:</span>
                        <span className="ml-1 font-medium">{profile.sessionPatterns.sessionFrequency}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Duration:</span>
                        <span className="ml-1 font-medium">{Math.round(profile.sessionPatterns.averageDuration)}m</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Platform:</span>
                        <span className="ml-1 font-medium">{profile.sessionPatterns.platformPreference}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Device:</span>
                        <span className="ml-1 font-medium">{profile.sessionPatterns.devicePreference}</span>
                      </div>
                    </div>
                  </div>

                  {/* Social Profile */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Social Metrics</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="bg-blue-50 p-2 rounded text-center">
                        <p className="font-bold text-blue-600">{profile.socialProfile.networkSize}</p>
                        <p className="text-xs text-gray-600">Network Size</p>
                      </div>
                      <div className="bg-green-50 p-2 rounded text-center">
                        <p className="font-bold text-green-600">{profile.socialProfile.influenceScore.toFixed(0)}</p>
                        <p className="text-xs text-gray-600">Influence</p>
                      </div>
                    </div>
                  </div>

                  {/* Top Skills */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Top Skills</h4>
                    <div className="space-y-1">
                      {Object.entries(profile.skillDevelopment.technicalSkills)
                        .sort(([,a], [,b]) => b - a)
                        .slice(0, 2)
                        .map(([skill, level]) => (
                          <div key={skill} className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">{skill}</span>
                            <span className="font-medium">{level.toFixed(0)}%</span>
                          </div>
                        ))}
                    </div>
                  </div>

                  {/* Wellbeing Indicators */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Wellbeing</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-gray-600">Satisfaction:</span>
                        <span className="ml-1 font-medium text-green-600">{profile.wellbeingIndicators.satisfactionLevel.toFixed(0)}%</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Balance:</span>
                        <span className="ml-1 font-medium text-blue-600">{profile.wellbeingIndicators.balanceScore.toFixed(0)}%</span>
                      </div>
                    </div>
                  </div>

                  <Button variant="outline" size="sm" className="w-full">
                    <Eye className="h-4 w-4 mr-2" />
                    View Full Profile
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredInsights.map((insight) => (
              <motion.div
                key={insight.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="group"
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        {getInsightIcon(insight.category)}
                        <Badge variant="outline" className="capitalize">
                          {insight.category.replace('_', ' ')}
                        </Badge>
                      </div>
                      <Badge className={getImpactColor(insight.impact)}>
                        {insight.impact}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg leading-snug">{insight.insight}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">World:</span>
                      <span className="font-medium">{insight.worldContext}</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Confidence:</span>
                      <span className="font-medium">{insight.confidence.toFixed(1)}%</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Business Value:</span>
                      <span className="font-medium text-green-600">{insight.businessValue}</span>
                    </div>
                    
                    <div className="border-t pt-4">
                      <h4 className="font-medium text-gray-900 mb-2">Recommendation</h4>
                      <p className="text-sm text-gray-600 leading-relaxed">{insight.recommendation}</p>
                    </div>
                    
                    <div className="border-t pt-4">
                      <h4 className="font-medium text-gray-900 mb-2">Affected Segments</h4>
                      <div className="flex flex-wrap gap-1">
                        {insight.affectedUserSegments.map((segment, i) => (
                          <Badge key={i} variant="secondary" className="text-xs">{segment}</Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Clock className="h-3 w-3" />
                      <span>{insight.implementation.timeframe}</span>
                      <span>•</span>
                      <span className="capitalize">{insight.implementation.complexity} complexity</span>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Eye className="h-4 w-4 mr-2" />
                        Details
                      </Button>
                      <Button size="sm" className="flex-1">
                        <Rocket className="h-4 w-4 mr-2" />
                        Implement
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="realtime" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Real-time Active Users */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Real-time Active Users
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsLineChart data={engagementMetrics.slice(-12)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="activeUsers" stroke="#8B5CF6" strokeWidth={2} />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Real-time Technical Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gauge className="h-5 w-5" />
                  Technical Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsLineChart data={engagementMetrics.slice(-12)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="technicalQuality" stroke="#10B981" strokeWidth={2} />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Current System Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Monitor className="h-5 w-5" />
                Current System Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-2 rounded-full bg-green-100 flex items-center justify-center">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                  <p className="font-medium">System Health</p>
                  <p className="text-sm text-green-600">Operational</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-2 rounded-full bg-blue-100 flex items-center justify-center">
                    <Network className="h-8 w-8 text-blue-600" />
                  </div>
                  <p className="font-medium">Network</p>
                  <p className="text-sm text-blue-600">Stable</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-2 rounded-full bg-yellow-100 flex items-center justify-center">
                    <AlertTriangle className="h-8 w-8 text-yellow-600" />
                  </div>
                  <p className="font-medium">Load</p>
                  <p className="text-sm text-yellow-600">High</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-2 rounded-full bg-purple-100 flex items-center justify-center">
                    <Users className="h-8 w-8 text-purple-600" />
                  </div>
                  <p className="font-medium">Active Users</p>
                  <p className="text-sm text-purple-600">{engagementMetrics[engagementMetrics.length - 1]?.activeUsers.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default VirtualWorldEngagement;