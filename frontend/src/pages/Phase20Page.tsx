import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import MetaverseCommerceTracker from '../components/metaverse/MetaverseCommerceTracker';
import VirtualProductExperience from '../components/metaverse/VirtualProductExperience';
import VirtualWorldEngagement from '../components/metaverse/VirtualWorldEngagement';
import CrossRealityJourneyMapping from '../components/metaverse/CrossRealityJourneyMapping';
import NFTMarketplaceAnalytics from '../components/metaverse/NFTMarketplaceAnalytics';

const Phase20Page: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Phase 20: Metaverse & Immersive Analytics</h1>
        <p className="text-gray-600 mt-2">
          Next-generation analytics for virtual worlds, immersive experiences, and cross-reality commerce
        </p>
      </div>

      <Tabs defaultValue="metaverse" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="metaverse">Metaverse Analytics</TabsTrigger>
          <TabsTrigger value="virtual-store">Virtual Stores</TabsTrigger>
          <TabsTrigger value="immersive">Immersive Viz</TabsTrigger>
          <TabsTrigger value="cross-reality">Cross-Reality</TabsTrigger>
          <TabsTrigger value="nft">NFT Marketplace</TabsTrigger>
        </TabsList>

        <TabsContent value="metaverse" className="mt-6">
          <MetaverseCommerceTracker />
        </TabsContent>

        <TabsContent value="virtual-store" className="mt-6">
          <VirtualProductExperience />
        </TabsContent>

        <TabsContent value="immersive" className="mt-6">
          <VirtualWorldEngagement />
        </TabsContent>

        <TabsContent value="cross-reality" className="mt-6">
          <CrossRealityJourneyMapping />
        </TabsContent>

        <TabsContent value="nft" className="mt-6">
          <NFTMarketplaceAnalytics />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Phase20Page;