import axios from 'axios';
import { mockAuthApi, mockDashboardData } from './mockAuth';

// API endpoints for different microservices
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

const SERVICES = {
  DASHBOARD: `${API_BASE_URL}/api/v1/dashboard`,
  ANALYTICS: `${API_BASE_URL}/api/v1/analytics`, 
  AUTH: `${API_BASE_URL}/api/v1/auth`,
  LINK_TRACKING: 'http://localhost:8080',  // Link tracking service (Go)
  INTEGRATION: 'http://localhost:3001',    // Integration service (Node.js)
};

// Create axios instances for each service
const dashboardApi = axios.create({
  baseURL: SERVICES.DASHBOARD,
  headers: { 'Content-Type': 'application/json' },
});

const analyticsApi = axios.create({
  baseURL: SERVICES.ANALYTICS,
  headers: { 'Content-Type': 'application/json' },
});

const linkTrackingApi = axios.create({
  baseURL: SERVICES.LINK_TRACKING,
  headers: { 'Content-Type': 'application/json' },
});

const authApi = axios.create({
  baseURL: SERVICES.AUTH,
  headers: { 'Content-Type': 'application/json' },
});

const integrationApi = axios.create({
  baseURL: SERVICES.INTEGRATION,
  headers: { 'Content-Type': 'application/json' },
});

// Auth token management
export const setAuthToken = (token: string) => {
  const authHeader = `Bearer ${token}`;
  dashboardApi.defaults.headers.common['Authorization'] = authHeader;
  analyticsApi.defaults.headers.common['Authorization'] = authHeader;
  linkTrackingApi.defaults.headers.common['Authorization'] = authHeader;
  integrationApi.defaults.headers.common['Authorization'] = authHeader;
  localStorage.setItem('auth_token', token);
};

export const clearAuthToken = () => {
  delete dashboardApi.defaults.headers.common['Authorization'];
  delete analyticsApi.defaults.headers.common['Authorization'];
  delete linkTrackingApi.defaults.headers.common['Authorization'];
  delete integrationApi.defaults.headers.common['Authorization'];
  localStorage.removeItem('auth_token');
};

// Initialize token from localStorage on startup
const token = localStorage.getItem('auth_token');
if (token) {
  setAuthToken(token);
}

// Detect if we're in mock mode (backend services not available)
const isMockMode = () => {
  return import.meta.env.VITE_MOCK_MODE === 'true' || localStorage.getItem('mock_mode') === 'true';
};

// Auto-detect mock mode by checking if dashboard service is available
let mockModeDetected = false;

// Auth API (through Dashboard service or mock)
export const authApi = {
  login: async (email: string, password: string) => {
    try {
      if (isMockMode() || mockModeDetected) {
        return await mockAuthApi.login(email, password);
      }
      
      const { data } = await dashboardApi.post('/api/auth/login', {
        email,
        password,
      });
      return data;
    } catch (error: any) {
      // If connection refused, switch to mock mode
      if (error.code === 'ERR_NETWORK' || error.message?.includes('ECONNREFUSED')) {
        console.warn('Backend not available, switching to mock mode');
        mockModeDetected = true;
        localStorage.setItem('mock_mode', 'true');
        return await mockAuthApi.login(email, password);
      }
      throw error;
    }
  },

  register: async (email: string, password: string, firstName: string, lastName: string = '', companyName?: string) => {
    try {
      if (isMockMode() || mockModeDetected) {
        return await mockAuthApi.register(email, password, firstName, lastName, companyName);
      }
      
      const { data } = await dashboardApi.post('/api/auth/register', {
        email,
        password,
        firstName,
        lastName: lastName || 'User',
        companyName,
      });
      return data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('ECONNREFUSED')) {
        console.warn('Backend not available, switching to mock mode');
        mockModeDetected = true;
        localStorage.setItem('mock_mode', 'true');
        return await mockAuthApi.register(email, password, firstName, lastName, companyName);
      }
      throw error;
    }
  },

  me: async () => {
    try {
      if (isMockMode() || mockModeDetected) {
        return await mockAuthApi.me();
      }
      
      const { data } = await dashboardApi.get('/api/auth/me');
      return data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('ECONNREFUSED')) {
        console.warn('Backend not available, switching to mock mode');
        mockModeDetected = true;
        localStorage.setItem('mock_mode', 'true');
        return await mockAuthApi.me();
      }
      throw error;
    }
  },
};

// Dashboard API - Aggregated dashboard data
export const dashboardService = {
  getOverview: async (params?: { date_from?: string; date_to?: string; period?: string }) => {
    try {
      if (isMockMode() || mockModeDetected) {
        await new Promise(resolve => setTimeout(resolve, 500)); // Simulate delay
        return {
          success: true,
          data: mockDashboardData.overview,
        };
      }
      
      const { data } = await dashboardApi.get('/api/dashboard/overview', { params });
      return data;
    } catch (error: any) {
      if (error.code === 'ERR_NETWORK' || error.message?.includes('ECONNREFUSED')) {
        mockModeDetected = true;
        localStorage.setItem('mock_mode', 'true');
        await new Promise(resolve => setTimeout(resolve, 500));
        return {
          success: true,
          data: mockDashboardData.overview,
        };
      }
      throw error;
    }
  },

  getMetrics: async (params?: { date_from?: string; date_to?: string; compare_period?: boolean }) => {
    const { data } = await dashboardApi.get('/api/dashboard/metrics', { params });
    return data;
  },

  getRecentActivity: async (params?: { limit?: number }) => {
    const { data } = await dashboardApi.get('/api/dashboard/recent-activity', { params });
    return data;
  },

  getTopLinks: async (params?: { metric?: string; limit?: number; date_from?: string; date_to?: string }) => {
    const { data } = await dashboardApi.get('/api/dashboard/top-links', { params });
    return data;
  },

  getAlerts: async (params?: { limit?: number }) => {
    const { data } = await dashboardApi.get('/api/dashboard/alerts', { params });
    return data;
  },

  getConversionFunnel: async (params?: { date_from?: string; date_to?: string; platform?: string }) => {
    const { data } = await dashboardApi.get('/api/dashboard/conversion-funnel', { params });
    return data;
  },

  getRevenueChart: async (params?: { date_from?: string; date_to?: string; granularity?: string; currency?: string }) => {
    const { data } = await dashboardApi.get('/api/dashboard/revenue-chart', { params });
    return data;
  },

  getGeographicData: async (params?: { date_from?: string; date_to?: string; limit?: number }) => {
    const { data } = await dashboardApi.get('/api/dashboard/geographic-data', { params });
    return data;
  },

  getPlatformPerformance: async (params?: { date_from?: string; date_to?: string }) => {
    const { data } = await dashboardApi.get('/api/dashboard/platform-performance', { params });
    return data;
  },
};

// Analytics API - Direct analytics service calls
export const analyticsService = {
  getSummary: async (params?: { 
    date_from?: string; 
    date_to?: string; 
    platform?: string; 
    link_id?: string; 
    integration_id?: string;
  }) => {
    const { data } = await analyticsApi.get('/api/analytics/summary', { params });
    return data;
  },

  getLinkAnalytics: async (linkId: string, params?: { 
    date_from?: string; 
    date_to?: string; 
    granularity?: string;
  }) => {
    const { data } = await analyticsApi.get(`/api/analytics/links/${linkId}`, { params });
    return data;
  },

  getClickAnalytics: async (params?: {
    link_id?: string;
    date_from?: string;
    date_to?: string;
    country?: string;
    device_type?: string;
    limit?: number;
    offset?: number;
  }) => {
    const { data } = await analyticsApi.get('/api/analytics/clicks', { params });
    return data;
  },

  getConversionAnalytics: async (params?: {
    link_id?: string;
    platform?: string;
    date_from?: string;
    date_to?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }) => {
    const { data } = await analyticsApi.get('/api/analytics/conversions', { params });
    return data;
  },

  getTopPerformers: async (params?: {
    metric?: string;
    date_from?: string;
    date_to?: string;
    limit?: number;
  }) => {
    const { data } = await analyticsApi.get('/api/analytics/top-performers', { params });
    return data;
  },

  getGeographicAnalytics: async (params?: {
    link_id?: string;
    date_from?: string;
    date_to?: string;
    level?: string;
  }) => {
    const { data } = await analyticsApi.get('/api/analytics/geographic', { params });
    return data;
  },

  getTimeSeries: async (params?: {
    metric?: string;
    granularity?: string;
    date_from?: string;
    date_to?: string;
    link_id?: string;
    platform?: string;
  }) => {
    const { data } = await analyticsApi.get('/api/analytics/time-series', { params });
    return data;
  },

  exportData: async (format: 'json' | 'csv' = 'json', params?: {
    date_from?: string;
    date_to?: string;
  }) => {
    const response = await analyticsApi.get('/api/analytics/export', { 
      params: { ...params, format },
      responseType: format === 'csv' ? 'text' : 'json'
    });
    return response.data;
  },
};

// Link Tracking API (Go service)
export const linkTrackingService = {
  getLinks: async (params?: { limit?: number; offset?: number; tenant_id?: string }) => {
    const { data } = await linkTrackingApi.get('/api/v1/links/list', { params });
    return data;
  },

  getLinkById: async (id: string, tenant_id?: string) => {
    const params = tenant_id ? { tenant_id } : {};
    const { data } = await linkTrackingApi.get(`/api/v1/links/${id}`, { params });
    return data;
  },

  createLink: async (linkData: { 
    target_url: string; 
    title?: string; 
    campaign_id?: string;
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
    tenant_id: string;
  }) => {
    const { data } = await linkTrackingApi.post('/api/v1/links', linkData);
    return data;
  },

  updateLink: async (id: string, linkData: { 
    title?: string; 
    is_active?: boolean;
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
    tenant_id?: string;
  }) => {
    const { data } = await linkTrackingApi.put(`/api/v1/links/${id}`, linkData);
    return data;
  },

  deleteLink: async (id: string, tenant_id?: string) => {
    const params = tenant_id ? { tenant_id } : {};
    const { data } = await linkTrackingApi.delete(`/api/v1/links/${id}`, { params });
    return data;
  },

  getLinkClicks: async (id: string, params?: {
    date_from?: string;
    date_to?: string;
    limit?: number;
    offset?: number;
    tenant_id?: string;
  }) => {
    const { data } = await linkTrackingApi.get(`/api/v1/links/${id}/clicks`, { params });
    return data;
  },
};

// Integration API - E-commerce platform integrations
export const integrationService = {
  getIntegrations: async () => {
    const { data } = await integrationApi.get('/integrations');
    return data;
  },

  createIntegration: async (integrationData: {
    platform: 'shopify' | 'woocommerce' | 'amazon';
    store_name?: string;
    store_url?: string;
    api_credentials: Record<string, unknown>;
    webhook_secret?: string;
  }) => {
    const { data } = await integrationApi.post('/integrations', integrationData);
    return data;
  },

  updateIntegration: async (id: string, integrationData: {
    store_name?: string;
    store_url?: string;
    api_credentials?: Record<string, unknown>;
    webhook_secret?: string;
    is_active?: boolean;
  }) => {
    const { data } = await integrationApi.put(`/integrations/${id}`, integrationData);
    return data;
  },

  deleteIntegration: async (id: string) => {
    const { data } = await integrationApi.delete(`/integrations/${id}`);
    return data;
  },

  syncIntegration: async (id: string) => {
    const { data } = await integrationApi.post(`/integrations/${id}/sync`);
    return data;
  },

  getOrders: async (params?: {
    integration_id?: string;
    date_from?: string;
    date_to?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }) => {
    const { data } = await integrationApi.get('/orders', { params });
    return data;
  },
};

// Health check for all services
export const healthService = {
  checkAllServices: async () => {
    const services = [
      { name: 'dashboard', api: dashboardApi, endpoint: '/health' },
      { name: 'analytics', api: analyticsApi, endpoint: '/health' },
      { name: 'link-tracking', api: linkTrackingApi, endpoint: '/health' },
      { name: 'integration', api: integrationApi, endpoint: '/health' },
    ];

    const results = await Promise.allSettled(
      services.map(async (service) => {
        try {
          const response = await service.api.get(service.endpoint, { timeout: 5000 });
          return {
            name: service.name,
            status: 'healthy',
            response_time: response.headers['x-response-time'] || 'unknown',
            data: response.data,
          };
        } catch (error) {
          return {
            name: service.name,
            status: 'unhealthy',
            error: error instanceof Error ? error.message : 'Unknown error',
          };
        }
      })
    );

    return {
      timestamp: new Date().toISOString(),
      services: results.map((result, index) => ({
        name: services[index].name,
        ...(result.status === 'fulfilled' ? result.value : { status: 'error', error: result.reason }),
      })),
    };
  },
};

// Compatibility exports for existing components
export const linksApi = {
  getAll: () => linkTrackingService.getLinks(),
  getById: (id: string) => linkTrackingService.getLinkById(id),
  create: (data: any) => linkTrackingService.createLink(data),
  update: (id: string, data: any) => linkTrackingService.updateLink(id, data),
  delete: (id: string) => linkTrackingService.deleteLink(id),
};

export const analyticsApiCompat = {
  getOverview: () => analyticsService.getSummary(),
  exportData: (format: 'json' | 'csv') => analyticsService.exportData(format),
};

export const dashboardApiCompat = {
  getData: () => dashboardService.getOverview(),
};

export default {
  auth: authApi,
  dashboard: dashboardService,
  analytics: analyticsService,
  linkTracking: linkTrackingService,
  integration: integrationService,
  health: healthService,
};