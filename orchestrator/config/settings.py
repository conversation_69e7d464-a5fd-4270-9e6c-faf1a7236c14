"""
Configuration settings for the Agent Orchestrator
"""

import os
from pathlib import Path

# Base paths
PROJECT_ROOT = Path(__file__).parent.parent.parent
ORCHESTRATOR_ROOT = Path(__file__).parent.parent
WORKTREES_DIR = PROJECT_ROOT / "worktrees"
TASKS_FILE = PROJECT_ROOT / "tasks.md"

# Git settings
DEFAULT_BRANCH = "main"
WORKTREE_PREFIX = "agent-"

# Tmux settings
TMUX_SESSION_PREFIX = "agent-"
TMUX_PANE_CAPTURE_INTERVAL = 30  # seconds

# Agent settings
CLAUDE_CODE_COMMAND = "claude-code"
DEFAULT_TOOLS = ["edit", "write", "bash", "replace", "read"]
MAX_CONCURRENT_AGENTS = 5
AGENT_TIMEOUT = 3600  # 1 hour in seconds

# Monitoring settings
STATUS_CHECK_INTERVAL = 60  # seconds
MAX_IDLE_TIME = 300  # 5 minutes
LOG_RETENTION_DAYS = 7

# Cleanup settings
CLEANUP_RETENTION_DAYS = 14  # Keep completed tasks for 2 weeks
MAX_WORKTREES = 20
MAX_DISK_USAGE_GB = 10
CLEANUP_LOG_RETENTION_DAYS = 30

# Task status values
class TaskStatus:
    UNCLAIMED = "unclaimed"
    CLAIMED = "claimed"
    IN_PROGRESS = "in-progress"
    INTERVENTION_REQUIRED = "intervention-required"
    COMPLETED = "completed"
    FAILED = "failed"
    ARCHIVED = "archived"

# Task priorities
class TaskPriority:
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

# Logging configuration
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
    },
    'handlers': {
        'default': {
            'level': 'INFO',
            'formatter': 'standard',
            'class': 'logging.StreamHandler',
        },
        'file': {
            'level': 'DEBUG',
            'formatter': 'standard',
            'class': 'logging.FileHandler',
            'filename': str(ORCHESTRATOR_ROOT / 'logs' / 'orchestrator.log'),
            'mode': 'a',
        },
    },
    'loggers': {
        '': {
            'handlers': ['default', 'file'],
            'level': 'INFO',
            'propagate': False
        }
    }
}

# Environment variables
ENVIRONMENT = os.getenv('ORCHESTRATOR_ENV', 'development')
DEBUG = os.getenv('DEBUG', 'false').lower() == 'true'

# Feature flags
ENABLE_AUTO_MERGE = os.getenv('ENABLE_AUTO_MERGE', 'false').lower() == 'true'
ENABLE_CONFLICT_RESOLUTION = os.getenv('ENABLE_CONFLICT_RESOLUTION', 'true').lower() == 'true'
ENABLE_RESOURCE_LIMITS = os.getenv('ENABLE_RESOURCE_LIMITS', 'true').lower() == 'true'