const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const logger = require('./utils/logger');
const { connectDatabase } = require('./database');
const { connectRedis } = require('./redis');
const { initMetrics } = require('./metrics');
const routes = require('./routes');
const errorHandler = require('./middleware/errorHandler');
const ScheduledSyncService = require('./services/scheduledSync');

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize scheduled sync service
const scheduledSync = new ScheduledSyncService();

// Trust proxy
app.set('trust proxy', 1);

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Initialize metrics
initMetrics(app);

// Health check routes
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'healthy', 
    service: 'integration-service',
    timestamp: new Date().toISOString() 
  });
});

app.get('/ready', async (req, res) => {
  try {
    // Check database connection
    const db = require('./database');
    await db.query('SELECT 1');
    
    // Check Redis connection
    const redis = require('./redis');
    await redis.ping();
    
    res.status(200).json({ 
      status: 'ready',
      service: 'integration-service',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Readiness check failed:', error);
    res.status(503).json({ 
      status: 'not ready',
      service: 'integration-service',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// API routes
app.use('/api/v1', routes);

// Error handling
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`
  });
});

// Start server
async function startServer() {
  try {
    // Connect to database
    await connectDatabase();
    logger.info('Database connected successfully');

    // Connect to Redis
    await connectRedis();
    logger.info('Redis connected successfully');

    // Start scheduled sync service
    if (process.env.ENABLE_SCHEDULED_SYNC !== 'false') {
      scheduledSync.start();
      logger.info('Scheduled sync service started');
    }

    // Start HTTP server
    app.listen(PORT, () => {
      logger.info(`Integration service started on port ${PORT}`);
      logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  logger.info('Received SIGINT, shutting down gracefully...');

  try {
    // Stop scheduled sync service
    scheduledSync.stop();

    const db = require('./database');
    const redis = require('./redis');

    await db.end();
    await redis.quit();

    logger.info('Connections closed, exiting...');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, shutting down gracefully...');

  try {
    // Stop scheduled sync service
    scheduledSync.stop();

    const db = require('./database');
    const redis = require('./redis');

    await db.end();
    await redis.quit();

    logger.info('Connections closed, exiting...');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

startServer();